#!/usr/bin/env python3
"""
Test script to verify the updated scraper finds episodes correctly.
"""

import sys
import os
import logging

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.scraping.scraper import DonghuaScraper
from config import Config

def test_updated_scraper():
    """Test the updated scraper's _scrape_episodes method."""
    
    # Configure logging for testing
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('test_updated_scraper.log', encoding='utf-8')
        ],
        force=True
    )
    
    print("Testing updated scraper...")
    logging.info("Testing updated scraper...")
    
    # Create scraper instance
    scraper = DonghuaScraper()
    
    try:
        # Test the _scrape_episodes method
        print("Testing _scrape_episodes method...")
        logging.info("Testing _scrape_episodes method...")
        
        episode_list = scraper._scrape_episodes()
        
        print("=== SCRAPING RESULTS ===")
        print(f"Found {len(episode_list)} episodes:")
        logging.info(f"Found {len(episode_list)} episodes:")
        
        for i, (title, url) in enumerate(episode_list[:10]):  # Show first 10
            print(f"  {i+1}. {title} -> {url}")
            logging.info(f"  {i+1}. {title} -> {url}")
        
        if len(episode_list) > 10:
            print(f"  ... and {len(episode_list) - 10} more episodes")
            logging.info(f"  ... and {len(episode_list) - 10} more episodes")
        
        print("=== TEST COMPLETED ===")
        logging.info("=== TEST COMPLETED ===")
        
        return episode_list
        
    except Exception as e:
        print(f"Error during testing: {e}")
        logging.error(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        scraper.close()

if __name__ == "__main__":
    print("Testing updated scraper...")
    print("=" * 50)
    
    result = test_updated_scraper()
    
    if result:
        print(f"\nTest completed successfully! Found {len(result)} episodes.")
        print("The scraper should now work correctly.")
    else:
        print("\nTest failed. Check the logs for error details.")
