# app/scheduler/scheduler.py
import schedule
import time
import threading
import logging

class Scheduler:
    def __init__(self):
        self._schedule = schedule.Scheduler()
        self._stop_event = threading.Event()
        self._thread = threading.Thread(target=self._run, daemon=True)

    def add_job(self, job_func, trigger_time):
        """
        Adds a job to the scheduler.

        :param job_func: The function to execute.
        :param trigger_time: The time to trigger the job (HH:MM format).
        """
        try:
            self._schedule.every().day.at(trigger_time).do(job_func)
            logging.info(f"Scheduled job '{job_func.__name__}' at {trigger_time} daily.")
        except Exception as e:
            logging.error(f"Failed to schedule job '{job_func.__name__}': {e}")

    def _run(self):
        """
        Runs the scheduler in a separate thread.
        """
        logging.info("Scheduler thread started.")
        while not self._stop_event.is_set():
            self._schedule.run_pending()
            time.sleep(1)
        logging.info("Scheduler thread stopped.")

    def start(self):
        """
        Starts the scheduler thread.
        """
        self._thread.start()

    def stop(self):
        """
        Stops the scheduler thread.
        """
        self._stop_event.set()
        self._thread.join()
