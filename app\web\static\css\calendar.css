/* Calendar Styles */

.calendar-container {
    background-color: rgba(45, 45, 58, 0.4); /* Reduced opacity from 0.7 to 0.4 */
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    margin-bottom: 30px;
}

.calendar-header {
    margin-bottom: 20px;
}

.calendar-navigation {
    display: flex;
    align-items: center;
}

/* Weekly Calendar View */
.weekly-calendar {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.calendar-day-container {
    background-color: rgba(50, 50, 65, 0.4); /* Reduced opacity from 0.7 to 0.4 */
    border-radius: 10px;
    padding: 15px;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.calendar-day-container:hover {
    background-color: rgba(60, 60, 75, 0.5); /* Reduced opacity from 0.8 to 0.5 */
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.calendar-day-container.today {
    background-color: rgba(70, 90, 120, 0.4); /* Reduced opacity from 0.7 to 0.4 */
    box-shadow: 0 0 0 2px rgba(100, 150, 255, 0.5);
}

.calendar-day-container.future {
    background-color: rgba(60, 60, 80, 0.3); /* Reduced opacity from 0.6 to 0.3 */
}

.day-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 10px;
    margin-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.weekday {
    font-weight: bold;
    font-size: 1.2rem;
    color: #ffffff;
}

.date {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.8);
}

.day-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.content-section-title {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.episodes, .predictions {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.episode-card, .prediction-card {
    padding: 12px;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.episode-card {
    background-color: rgba(80, 130, 190, 0.2); /* Reduced opacity from 0.3 to 0.2 */
    border-left: 3px solid #4a90e2;
}

.prediction-card {
    background-color: rgba(130, 80, 190, 0.15); /* Reduced opacity from 0.2 to 0.15 */
    border-left: 3px solid #9c6ade;
}

.episode-card:hover, .prediction-card:hover {
    transform: translateX(3px);
}

.episode-link, .prediction-link {
    color: #e0e0e0;
    text-decoration: none;
    display: block;
}

.episode-link:hover, .prediction-link:hover {
    color: #ffffff;
}

.episode-show-title, .prediction-title {
    font-weight: bold;
    font-size: 1rem;
    margin-bottom: 5px;
}

.episode-title {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
}

.prediction-label {
    font-size: 0.8rem;
    color: #b490e2;
    font-style: italic;
}

.no-content {
    padding: 20px;
    text-align: center;
    color: rgba(255, 255, 255, 0.5);
    font-style: italic;
}

/* Responsive adjustments */
@media (min-width: 992px) {
    .weekly-calendar {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 15px;
    }

    .calendar-day-container {
        height: 100%;
        min-height: 300px;
    }
}

@media (min-width: 768px) and (max-width: 991px) {
    .weekly-calendar {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 15px;
    }

    .calendar-day-container {
        min-height: 250px;
    }
}

@media (min-width: 576px) and (max-width: 767px) {
    .weekly-calendar {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
}

@media (max-width: 575px) {
    .calendar-container {
        padding: 10px;
    }

    .calendar-day-container {
        padding: 12px;
    }

    .day-header {
        padding-bottom: 8px;
        margin-bottom: 10px;
    }

    .weekday {
        font-size: 1.1rem;
    }

    .date {
        font-size: 1rem;
    }

    .episode-card, .prediction-card {
        padding: 10px;
    }

    .episode-show-title, .prediction-title {
        font-size: 0.9rem;
    }

    .episode-title, .prediction-label {
        font-size: 0.8rem;
    }
}
