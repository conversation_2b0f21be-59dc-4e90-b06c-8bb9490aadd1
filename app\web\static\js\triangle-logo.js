// triangle-logo.js - WebGL rotating glass triangle with Three.js

// Initialize the 3D scene when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Check if the container exists
    const container = document.getElementById('triangle-container');
    if (!container) return;

    // Set up scene, camera, and renderer
    const scene = new THREE.Scene();

    // Create a camera with perspective (field of view, aspect ratio, near plane, far plane)
    const camera = new THREE.PerspectiveCamera(60, 1, 0.1, 1000);
    camera.position.z = 5;

    // Create WebGL renderer with transparency
    const renderer = new THREE.WebGLRenderer({
        antialias: true,
        alpha: true
    });

    // Set renderer size to match container and set pixel ratio for better quality
    renderer.setSize(container.clientWidth, container.clientWidth); // Square aspect ratio
    renderer.setPixelRatio(window.devicePixelRatio);
    renderer.setClearColor(0x000000, 0); // Transparent background

    // Add the renderer's canvas to the container
    container.appendChild(renderer.domElement);

    // Create a group to hold the triangle
    const triangleGroup = new THREE.Group();
    scene.add(triangleGroup);

    // Create the DS text as a texture with better quality
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    canvas.width = 1024; // Higher resolution
    canvas.height = 1024;

    // Set text properties - make it extremely bold and clear
    context.fillStyle = 'white';
    context.font = 'bold 500px Arial, sans-serif'; // Even larger, bolder font
    context.textAlign = 'center';
    context.textBaseline = 'middle';

    // Clear the canvas to ensure no background
    context.clearRect(0, 0, canvas.width, canvas.height);

    // Add a stronger glow effect
    context.shadowColor = 'rgba(255, 255, 255, 1.0)';
    context.shadowBlur = 20;
    context.shadowOffsetX = 0;
    context.shadowOffsetY = 0;

    // Draw an outer glow first for extra visibility
    context.globalAlpha = 0.5;
    context.shadowBlur = 30;
    context.fillText('DS', canvas.width / 2, canvas.height / 2);

    // Reset for main text
    context.globalAlpha = 1.0;
    context.shadowBlur = 10;

    // Draw the text
    context.fillText('DS', canvas.width / 2, canvas.height / 2);

    // Create texture from canvas
    const texture = new THREE.CanvasTexture(canvas);
    texture.anisotropy = renderer.capabilities.getMaxAnisotropy(); // Sharper texture

    // Create a plane to display the text - brighter material
    const textGeometry = new THREE.PlaneGeometry(3, 3);
    const textMaterial = new THREE.MeshBasicMaterial({
        map: texture,
        transparent: true,
        color: 0xffffff,
        opacity: 1.0
    });
    const textMesh = new THREE.Mesh(textGeometry, textMaterial);
    textMesh.position.z = -1.5; // Position closer to the triangle for better visibility
    scene.add(textMesh);

    // Add a subtle glow behind the text to make it more visible
    const glowGeometry = new THREE.PlaneGeometry(3.2, 3.2);
    const glowMaterial = new THREE.MeshBasicMaterial({
        color: 0xffffff,
        transparent: true,
        opacity: 0.2,
        blending: THREE.AdditiveBlending
    });
    const glowMesh = new THREE.Mesh(glowGeometry, glowMaterial);
    glowMesh.position.z = -1.6; // Position just behind the text
    scene.add(glowMesh);

    // We'll remove the square background glow plane

    // Create a simple environment map for reflections
    const cubeRenderTarget = new THREE.WebGLCubeRenderTarget(256);
    const cubeCamera = new THREE.CubeCamera(0.1, 1000, cubeRenderTarget);
    scene.add(cubeCamera);

    // Update the environment map once
    cubeCamera.update(renderer, scene);

    // Use the environment map for the glass material
    const envMap = cubeRenderTarget.texture;

    // Create a proper triangular prism using a custom geometry
    // This will ensure proper refraction through flat faces
    const triangleGeometry = new THREE.BufferGeometry();

    // Define the vertices of a triangular prism
    const vertices = new Float32Array([
        // Front face (triangle)
        0.0, 1.5, 0.4,    // top
        -1.5, -1.5, 0.4,  // bottom left
        1.5, -1.5, 0.4,   // bottom right

        // Back face (triangle)
        0.0, 1.5, -0.4,   // top
        -1.5, -1.5, -0.4, // bottom left
        1.5, -1.5, -0.4,  // bottom right

        // Side faces (rectangles)
        // Left side
        0.0, 1.5, 0.4,    // front top
        -1.5, -1.5, 0.4,  // front bottom
        0.0, 1.5, -0.4,   // back top
        -1.5, -1.5, -0.4, // back bottom

        // Right side
        0.0, 1.5, 0.4,    // front top
        1.5, -1.5, 0.4,   // front bottom
        0.0, 1.5, -0.4,   // back top
        1.5, -1.5, -0.4,  // back bottom

        // Bottom side
        -1.5, -1.5, 0.4,  // front left
        1.5, -1.5, 0.4,   // front right
        -1.5, -1.5, -0.4, // back left
        1.5, -1.5, -0.4   // back right
    ]);

    // Define indices to create faces
    const indices = [
        // Front face
        0, 1, 2,

        // Back face
        3, 5, 4, // Note: winding order reversed for correct normal

        // Left side
        6, 8, 9,
        6, 9, 7,

        // Right side
        10, 11, 13,
        10, 13, 12,

        // Bottom side
        14, 16, 17,
        14, 17, 15
    ];

    // Set the attributes
    triangleGeometry.setAttribute('position', new THREE.BufferAttribute(vertices, 3));
    triangleGeometry.setIndex(indices);
    triangleGeometry.computeVertexNormals(); // Important for lighting

    // Create glass material with proper refraction - much clearer
    const glassMaterial = new THREE.MeshPhysicalMaterial({
        color: 0xffffff,
        metalness: 0.0,
        roughness: 0.0,
        transmission: 0.99, // Almost complete transmission for clarity
        transparent: true,
        opacity: 0.1, // Very low opacity for clear glass
        thickness: 0.5, // Thinner for less distortion
        ior: 1.45, // Slightly lower IOR for less distortion
        side: THREE.DoubleSide,
        envMap: envMap, // Use the environment map
        envMapIntensity: 0.5, // Lower intensity for less reflection
        clearcoat: 0.2, // Minimal clearcoat
        clearcoatRoughness: 0.01,
        premultipliedAlpha: true, // Helps with transparency rendering
        depthWrite: false // Disable depth writing for better transparency
    });

    const triangleMesh = new THREE.Mesh(triangleGeometry, glassMaterial);
    triangleGroup.add(triangleMesh);

    // Create a custom shader material for scintillating edges
    // This will make the edges shimmer based on the angle to the camera
    const edgeGeometry = new THREE.EdgesGeometry(triangleGeometry);

    // Create a custom shader material for the edges
    const edgeShaderMaterial = new THREE.ShaderMaterial({
        uniforms: {
            time: { value: 0 },
            color1: { value: new THREE.Color(0xffffff) },
            color2: { value: new THREE.Color(0x88ccff) }
        },
        vertexShader: `
            varying vec3 vNormal;
            varying vec3 vViewPosition;

            void main() {
                vNormal = normalize(normalMatrix * normal);
                vec4 mvPosition = modelViewMatrix * vec4(position, 1.0);
                vViewPosition = -mvPosition.xyz;
                gl_Position = projectionMatrix * mvPosition;
            }
        `,
        fragmentShader: `
            uniform float time;
            uniform vec3 color1;
            uniform vec3 color2;
            varying vec3 vNormal;
            varying vec3 vViewPosition;

            void main() {
                // Calculate the angle between view direction and normal
                vec3 viewDir = normalize(vViewPosition);
                float fresnel = dot(viewDir, vNormal);
                fresnel = clamp(1.0 - fresnel, 0.0, 1.0);

                // Add time-based shimmer effect
                float shimmer = sin(time * 3.0 + gl_FragCoord.x * 0.1 + gl_FragCoord.y * 0.1) * 0.5 + 0.5;

                // Combine fresnel and shimmer for scintillation effect
                float intensity = pow(fresnel, 3.0) * (shimmer * 0.8 + 0.2);

                // Mix colors based on intensity
                vec3 finalColor = mix(color1, color2, intensity);

                // Set final color with opacity based on intensity
                gl_FragColor = vec4(finalColor, intensity * 0.7);
            }
        `,
        transparent: true,
        side: THREE.DoubleSide
    });

    const edges = new THREE.LineSegments(edgeGeometry, edgeShaderMaterial);
    triangleGroup.add(edges);

    // Add ambient light - softer
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.3);
    scene.add(ambientLight);

    // Add directional light for better reflections
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(5, 5, 5);
    scene.add(directionalLight);

    // Add moving point lights for dynamic highlights and scintillation
    const pointLight1 = new THREE.PointLight(0x3498db, 1.5, 15);
    pointLight1.position.set(2, 2, 2);
    scene.add(pointLight1);

    const pointLight2 = new THREE.PointLight(0xe74c3c, 1.5, 15);
    pointLight2.position.set(-2, -2, 2);
    scene.add(pointLight2);

    // Add a spotlight specifically to illuminate the text
    const textSpotlight = new THREE.SpotLight(0xffffff, 1.5);
    textSpotlight.position.set(0, 0, 3);
    textSpotlight.target = textMesh;
    textSpotlight.angle = Math.PI / 4;
    textSpotlight.penumbra = 0.3;
    textSpotlight.decay = 1;
    textSpotlight.distance = 10;
    scene.add(textSpotlight);

    // Animation variables
    let time = 0;
    const rotationSpeed = 0.004; // Slightly slower rotation
    const wobbleAmount = 0.08; // Reduced wobble for better text visibility

    // Animation loop
    function animate() {
        requestAnimationFrame(animate);
        time += 0.01;

        // Update the time uniform for the edge shader
        if (edgeShaderMaterial.uniforms) {
            edgeShaderMaterial.uniforms.time.value = time;
        }

        // Rotate the triangle around the Y axis with a smooth sine wave motion
        // Use sine wave to slow down rotation at certain points for better text visibility
        const yRotation = Math.sin(time * 0.2) * 0.2; // Small oscillation
        triangleGroup.rotation.y += rotationSpeed;
        triangleGroup.rotation.y += yRotation * 0.01; // Add oscillation effect

        // Pause briefly when the flat face is aligned with the camera
        // This creates moments where the text is clearly visible
        const normalizedRotation = triangleGroup.rotation.y % (Math.PI * 2);
        const pausePoints = [0, Math.PI * 2/3, Math.PI * 4/3]; // Points where faces are aligned

        // Increase transparency when faces are aligned with camera for better text visibility
        let minAngleDiff = Math.PI;
        for (const point of pausePoints) {
            const angleDiff = Math.abs(normalizedRotation - point);
            minAngleDiff = Math.min(minAngleDiff, angleDiff);

            if (angleDiff < 0.2) {
                triangleGroup.rotation.y += rotationSpeed * 0.3; // Slow down at these points

                // Make glass more transparent when aligned
                glassMaterial.opacity = Math.max(0.05, glassMaterial.opacity - 0.001);
            } else {
                // Gradually return to normal opacity
                glassMaterial.opacity = Math.min(0.15, glassMaterial.opacity + 0.0005);
            }
        }

        // Add a slight wobble effect - reduced for better visibility
        triangleGroup.rotation.z = Math.sin(time) * wobbleAmount;
        triangleGroup.rotation.x = Math.cos(time * 0.5) * wobbleAmount * 0.3;

        // Subtle floating motion
        triangleGroup.position.y = Math.sin(time * 0.5) * 0.05; // Reduced floating

        // Move point lights for dynamic scintillation
        pointLight1.position.x = Math.sin(time * 0.7) * 3;
        pointLight1.position.y = Math.cos(time * 0.5) * 3;
        pointLight1.position.z = Math.sin(time * 0.3) * 2 + 3;

        pointLight2.position.x = Math.sin(time * 0.4 + Math.PI) * 3;
        pointLight2.position.y = Math.cos(time * 0.6 + Math.PI) * 3;
        pointLight2.position.z = Math.sin(time * 0.5 + Math.PI) * 2 + 3;

        // Adjust lighting based on face alignment
        if (minAngleDiff < 0.2) {
            // Increase spotlight intensity when faces are aligned
            textSpotlight.intensity = 2.5;
            // Make glass even clearer when aligned
            glassMaterial.opacity = 0.05;
            // Increase glow behind text when aligned
            glowMaterial.opacity = 0.3 + Math.sin(time * 3) * 0.05;
        } else {
            // Normal intensity otherwise
            textSpotlight.intensity = 1.5;
            // Return to normal opacity
            glassMaterial.opacity = 0.1;
            // Normal glow
            glowMaterial.opacity = 0.2;
        }

        // Periodically update the environment map for dynamic reflections
        if (Math.floor(time * 10) % 30 === 0) {
            textMesh.visible = false; // Hide text temporarily
            cubeCamera.update(renderer, scene);
            textMesh.visible = true; // Show text again
        }

        // Render the scene
        renderer.render(scene, camera);
    }

    // Start the animation loop
    animate();

    // Handle window resize
    window.addEventListener('resize', function() {
        // Keep the container square
        const size = container.clientWidth;

        // Update camera aspect ratio
        camera.aspect = 1;
        camera.updateProjectionMatrix();

        // Update renderer size
        renderer.setSize(size, size);
    });
});
