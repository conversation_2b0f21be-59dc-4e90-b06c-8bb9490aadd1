#!/usr/bin/env python3
"""
Test script to verify the updated scraper works with the new website structure.
This script tests the _get_episode_details method with a specific episode URL.
"""

import sys
import os
import logging

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.scraping.scraper import DonghuaScraper
from config import Config

def test_episode_scraping():
    """Test scraping a specific episode to verify the new structure handling."""

    print("Setting up logging...")

    # Configure logging for testing
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('test_scraper.log', encoding='utf-8')
        ],
        force=True  # Force reconfiguration
    )

    # Test URL - the episode you mentioned
    test_url = "https://donghuastream.org/perfect-world-episode-218-multiple-subtitles/"

    print(f"Testing scraper with URL: {test_url}")
    logging.info(f"Testing scraper with URL: {test_url}")

    try:
        print("Creating scraper instance...")
        # Create scraper instance
        scraper = DonghuaScraper()
        print("Scraper instance created successfully")
        logging.info("Scraper instance created successfully")

        # First, let's examine the page source to understand the structure
        print("Navigating to page to examine structure...")
        scraper._driver.get(test_url)

        # Wait for page to load
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        from selenium.webdriver.common.by import By

        WebDriverWait(scraper._driver, 20).until(
            EC.presence_of_element_located((By.TAG_NAME, 'body'))
        )

        import time
        time.sleep(5)  # Wait for JS to load

        # Save page source for analysis
        page_source = scraper._driver.page_source
        with open('page_source_debug.html', 'w', encoding='utf-8') as f:
            f.write(page_source)
        print("Page source saved to 'page_source_debug.html'")

        # Look for elements that might be the server selector
        print("Looking for potential server selection elements...")

        # Check for various possible selectors
        potential_selectors = [
            "button",
            "[class*='select']",
            "[class*='server']",
            "[data-action]",
            ".player",
            "#player",
            "[onclick]"
        ]

        for selector in potential_selectors:
            try:
                elements = scraper._driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    print(f"Found {len(elements)} elements with selector '{selector}':")
                    for i, elem in enumerate(elements[:3]):  # Show first 3
                        try:
                            text = elem.text.strip()[:50]
                            classes = elem.get_attribute('class') or ''
                            onclick = elem.get_attribute('onclick') or ''
                            print(f"  {i+1}. Text: '{text}', Classes: '{classes}', OnClick: '{onclick[:50]}'")
                        except:
                            print(f"  {i+1}. (Could not get element details)")
            except Exception as e:
                print(f"Error with selector '{selector}': {e}")

        # Test the _get_episode_details method
        print("\nTesting _get_episode_details method...")
        logging.info("Testing _get_episode_details method...")

        result = scraper._get_episode_details(test_url)

        iframe_srcs, show_title, thumbnail_url, is_preview, release_date = result

        print("=== SCRAPING RESULTS ===")
        print(f"Show Title: {show_title}")
        print(f"Thumbnail URL: {thumbnail_url}")
        print(f"Is Preview: {is_preview}")
        print(f"Release Date: {release_date}")

        logging.info("=== SCRAPING RESULTS ===")
        logging.info(f"Show Title: {show_title}")
        logging.info(f"Thumbnail URL: {thumbnail_url}")
        logging.info(f"Is Preview: {is_preview}")
        logging.info(f"Release Date: {release_date}")

        if iframe_srcs == 'no_iframe':
            print("No iframe found - this might indicate the episode is still a preview")
            logging.warning("No iframe found - this might indicate the episode is still a preview")
        elif not iframe_srcs:
            print("Empty iframe sources list")
            logging.warning("Empty iframe sources list")
        else:
            print(f"Found {len(iframe_srcs)} iframe source(s):")
            logging.info(f"Found {len(iframe_srcs)} iframe source(s):")
            for i, src in enumerate(iframe_srcs):
                print(f"  {i+1}. {src}")
                logging.info(f"  {i+1}. {src}")

        print("=== TEST COMPLETED ===")
        logging.info("=== TEST COMPLETED ===")

        # Return results for further analysis
        return result

    except Exception as e:
        print(f"Error during testing: {e}")
        logging.error(f"Error during testing: {e}")
        logging.exception("Full exception details:")
        import traceback
        print("Full traceback:")
        traceback.print_exc()
        return None
    finally:
        # Always close the scraper
        try:
            scraper.close()
            print("Scraper closed successfully")
        except:
            print("Error closing scraper or scraper was not created")

def test_decode_function():
    """Test the decode function with a sample base64 string."""
    print("Testing decode function...")

    try:
        from app.scraping.decode import decode_base64_to_iframe
        print("Successfully imported decode function")

        # Sample base64 string (you can replace this with actual data from the website)
        sample_base64 = "PGlmcmFtZSBzcmM9Imh0dHBzOi8vcGxheS5zdHJlYW1wbGF5LmNvLmluL2VtYmVkLzBEbjNqcHo0dU1oLTI4ciIgZnJhbWVib3JkZXI9IjAiIGFsbG93ZnVsbHNjcmVlbiB3ZWJraXRhbGxvd2Z1bGxzY3JlZW4gbW96YWxsb3dmdWxsc2NyZWVuIHdpZHRoPSI2NDAiIGhlaWdodD0iMzIwIj48L2lmcmFtZT4="

        print(f"Testing with sample base64: {sample_base64[:50]}...")

        decoded = decode_base64_to_iframe(sample_base64)
        print(f"Decoded iframe HTML: {decoded}")
        logging.info(f"Decoded iframe HTML: {decoded}")

        # Parse with BeautifulSoup to extract src
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(decoded, "html.parser")
        iframe = soup.find("iframe")

        if iframe and iframe.get("src"):
            src = iframe['src']
            print(f"Extracted iframe src: {src}")
            logging.info(f"Extracted iframe src: {src}")
        else:
            print("No iframe src found in decoded HTML")
            logging.warning("No iframe src found in decoded HTML")

    except ImportError as e:
        print(f"Import error: {e}")
        logging.error(f"Import error: {e}")
    except Exception as e:
        print(f"Error testing decode function: {e}")
        logging.error(f"Error testing decode function: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("Testing updated scraper...")
    print("=" * 50)
    
    # Test the decode function first
    print("1. Testing decode function...")
    test_decode_function()
    
    print("\n2. Testing episode scraping...")
    result = test_episode_scraping()
    
    if result:
        print("\nTest completed successfully!")
        print("Check the logs for detailed information.")
    else:
        print("\nTest failed. Check the logs for error details.")
