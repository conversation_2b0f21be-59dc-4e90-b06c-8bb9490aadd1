# config.py
import os
import threading
import logging
import logging.config

class Config:
    BASE_URL = "https://donghuastream.org/"
    BASE_DIR = os.path.dirname(os.path.abspath(__file__))
    DATABASE_PATH = os.path.join(BASE_DIR, 'donghuastream.db')
    LOG_FILE = os.path.join(BASE_DIR, 'scraper.log')
    LOG_LEVEL = logging.INFO

    # Scraper configurations
    SCRAPER_SCHEDULE_TIME = "04:30"  # Time to run the scraper daily (UTC)
    GECKODRIVER_PATH = os.path.join(BASE_DIR, 'drivers', 'geckodriver.exe')  # Update as per your setup
    LOGS_DIR = os.path.join(BASE_DIR, 'logs')
    SCREENSHOT_DELAY = 2  # Seconds to wait after certain actions
    SCROLL_PAUSE_TIME = 10  # Seconds to wait during scrolling

    # WebDriver preferences
    FIREFOX_HEADLESS = True
    FIREFOX_ACCEPT_UNTRUSTED_CERTS = True
    FIREFOX_ASSUME_UNTRUSTED_ISSUER = False
    FIREFOX_DISABLE_NOTIFICATIONS = True
    FIREFOX_DISABLE_PUSH = True

    # WebDriver wait times
    PAGE_LOAD_TIMEOUT = 20  # Seconds to wait for the page to load
    ARTICLE_LOAD_TIMEOUT = 20  # Seconds to wait for articles to load
    SCROLL_WAIT_TIMEOUT = 10  # Seconds to wait during scrolling

    # General String Constants
    ERROR_PAGE_LOAD = "Failed to load the page."
    LOG_FILE_CREATED = "Log file created successfully."
    DUPLICATE_EPISODE_SKIPPED = "Duplicate or invalid episode found and skipped."
    NO_IFRAME_FOUND = "No iframe srcs found on page. Skipping."
    EPISODE_INSERTED = "Inserted new episode: {title} -> {iframe_src}"



    # Threading lock to prevent concurrent scraping
    scraper_lock = threading.Lock()

# Configure logging
logging.config.dictConfig({
    'version': 1,
    'formatters': {'default': {
        'format': '%(asctime)s - %(levelname)s - %(message)s',
    }},
    'handlers': {'file': {
        'class': 'logging.FileHandler',
        'filename': Config.LOG_FILE,
        'formatter': 'default',
        'encoding': 'utf-8',  # Explicitly set UTF-8 encoding for log files
    }},
    'root': {
        'level': Config.LOG_LEVEL,
        'handlers': ['file']
    },
})
