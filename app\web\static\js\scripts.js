// Dark mode toggle functionality
const bodyElement = document.body;
const toggleButton = document.getElementById("darkModeToggle");

// Check saved theme on page load
if (localStorage.getItem("theme") === "dark") {
    bodyElement.classList.add("dark-mode");
    toggleButton.innerText = "Toggle Light Mode";
    toggleButton.classList.remove("btn-outline-dark");
    toggleButton.classList.add("btn-outline-light");
}

// Toggle dark mode on button click
toggleButton.addEventListener("click", function() {
    bodyElement.classList.toggle("dark-mode");

    if (bodyElement.classList.contains("dark-mode")) {
        toggleButton.innerText = "Toggle Light Mode";
        toggleButton.classList.remove("btn-outline-dark");
        toggleButton.classList.add("btn-outline-light");
        localStorage.setItem("theme", "dark");
    } else {
        toggleButton.innerText = "Toggle Dark Mode";
        toggleButton.classList.remove("btn-outline-light");
        toggleButton.classList.add("btn-outline-dark");
        localStorage.setItem("theme", "light");
    }
});

// Select All functionality for checkboxes
const selectAllCheckbox = document.getElementById("select-all-checkbox");
const showCheckboxes = document.querySelectorAll(".show-checkbox");

selectAllCheckbox.addEventListener("change", function() {
    showCheckboxes.forEach(function(checkbox) {
        checkbox.checked = selectAllCheckbox.checked;
    });
});

// Trigger Scrape button functionality
document.getElementById("triggerScrapeButton").addEventListener("click", function() {
    fetch("/trigger-scrape", {
        method: "POST",
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Failed to trigger scrape');
        }
    })
    .catch((error) => {
        console.error('Error:', error);
        alert('An error occurred while triggering the scrape.');
    });
});

// Auto-refresh the page every 10 seconds if no unwatched episodes
if (document.querySelector(".center-message")) {
    setTimeout(function() {
        window.location.reload(1);
    }, 10000); // Refresh every 10 seconds
}
window.addEventListener('scroll', function() {
    const background = document.body;
    const scrolled = window.scrollY;
    background.style.backgroundPositionY = `${scrolled * 0.05}px`; // Adjust 0.2 for more/less parallax
});
  