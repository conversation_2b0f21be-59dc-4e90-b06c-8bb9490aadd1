<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DonghuaStream - Calendar</title>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet" crossorigin="anonymous">

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Link to external CSS -->
    <link href="/static/css/index.css" rel="stylesheet">
    <link href="/static/css/calendar.css" rel="stylesheet">
    <link href="/static/css/realtime.css" rel="stylesheet">

    <!-- Custom DS Logo Script -->
    <script src="/static/js/ds-logo.js"></script>
</head>
<body>
    {% with active_page='calendar' %}
    {% include "sidebar.html" %}
    {% endwith %}

    <div class="content-wrapper">
        <div class="container-fluid">
            <!-- Main content -->
            <main class="main-content">
                <div class="calendar-header d-flex justify-content-between align-items-center mb-4">
                    <h2 class="h2">📅 {{ week_range }}</h2>
                    <div class="calendar-navigation">
                        <a href="/calendar?date={{ prev_week }}" class="btn btn-outline-primary me-2">
                            <i class="fas fa-chevron-left"></i> Previous Week
                        </a>
                        <a href="/calendar?date={{ today }}" class="btn btn-outline-secondary me-2">
                            Today
                        </a>
                        <a href="/calendar?date={{ next_week }}" class="btn btn-outline-primary">
                            Next Week <i class="fas fa-chevron-right"></i>
                        </a>
                    </div>
                </div>

                <div class="calendar-container">
                    <!-- Weekly view of episodes -->
                    <div class="weekly-calendar">
                        {% for day in days %}
                            <div class="calendar-day-container {% if day.is_today %}today{% endif %} {% if day.is_future %}future{% endif %}">
                                <div class="day-header">
                                    <div class="weekday">{{ day.weekday }}</div>
                                    <div class="date">{{ day.day }}</div>
                                </div>

                                <div class="day-content">
                                    {% if day.episodes %}
                                        <div class="episodes">
                                            <h6 class="content-section-title">Released Episodes</h6>
                                            {% for episode in day.episodes %}
                                                <div class="episode-card {% if episode[6] == 1 %}watched-episode{% endif %} {% if episode[7] == 1 %}preview-episode{% endif %}">
                                                    {% if episode[7] == 1 %}
                                                        <!-- For preview episodes, add data attributes and use JavaScript to handle the check -->
                                                        <a href="#" class="episode-link preview-check-link"
                                                           data-episode-id="{{ episode[0] }}"
                                                           data-episode-title="{{ episode[1] }}"
                                                           data-show-id="{{ episode[3] }}">
                                                            <div class="episode-show-title">{{ episode[4] }}</div>
                                                            <div class="episode-title" data-full-title="{{ episode[1] }}">Episode <span class="episode-number"></span></div>
                                                            {% if episode[6] == 1 %}
                                                                <div class="watched-indicator"><i class="fas fa-check-circle"></i> Watched</div>
                                                            {% endif %}
                                                            <div class="preview-indicator"><i class="fas fa-exclamation-triangle"></i> Preview (Click to re-check)</div>
                                                        </a>
                                                    {% else %}
                                                        <!-- For regular episodes, link to the show page -->
                                                        <a href="/show/{{ episode[3] }}" class="episode-link">
                                                            <div class="episode-show-title">{{ episode[4] }}</div>
                                                            <div class="episode-title" data-full-title="{{ episode[1] }}">Episode <span class="episode-number"></span></div>
                                                            {% if episode[6] == 1 %}
                                                                <div class="watched-indicator"><i class="fas fa-check-circle"></i> Watched</div>
                                                            {% endif %}
                                                        </a>
                                                    {% endif %}
                                                </div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}

                                    {% if day.predictions %}
                                        <div class="predictions">
                                            <h6 class="content-section-title">Predicted Releases</h6>
                                            {% for show_id, title, interval in day.predictions %}
                                                <div class="prediction-card">
                                                    <a href="/show/{{ show_id }}" class="prediction-link">
                                                        <div class="prediction-title">{{ title }}</div>
                                                        <div class="prediction-label">
                                                            {% if interval == 7 %}
                                                                Weekly release pattern
                                                            {% elif interval == 3 or interval == 4 %}
                                                                {{ interval }}-day release pattern
                                                            {% else %}
                                                                {{ interval }}-day release pattern
                                                            {% endif %}
                                                        </div>
                                                    </a>
                                                </div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}

                                    {% if not day.episodes and not day.predictions %}
                                        <div class="no-content">
                                            <p>No episodes for this day</p>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </main>
        </div>
    </div> <!-- End content-wrapper -->

    <!-- Bootstrap 5 JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js" crossorigin="anonymous"></script>

    <!-- Real-time WebSocket Client -->
    <script src="/static/js/realtime.js"></script>

    <!-- Custom JavaScript -->
    <script>
        // Function to extract episode number from full title
        function extractEpisodeNumber(fullTitle) {
            if (!fullTitle) return "??";

            // Try to match patterns like "Episode XX" or "Ep XX" or just a number after various separators
            const patterns = [
                /episode\s+(\d+)/i,                // "Episode 123"
                /ep\s+(\d+)/i,                    // "Ep 123"
                /\s+(\d+)\s*$/,                  // Title ending with a number
                /\s+(\d+)\s+/,                   // Number in the middle with spaces
                /\[(\d+)\]/,                     // Number in square brackets [123]
                /\s+(\d+)\s+multiple\s+subtitles/i, // "123 Multiple Subtitles"
                /season\s+\d+\s+episode\s+(\d+)/i  // "Season X Episode 123"
            ];

            for (const pattern of patterns) {
                const match = fullTitle.match(pattern);
                if (match && match[1]) {
                    return match[1];
                }
            }

            // If no pattern matches, try to find any number in the title
            const anyNumberMatch = fullTitle.match(/(\d+)/);
            if (anyNumberMatch && anyNumberMatch[1]) {
                return anyNumberMatch[1];
            }

            // If no pattern matches, return a default
            return "??";
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Process all episode titles to extract episode numbers
            document.querySelectorAll('.episode-title').forEach(function(titleElement) {
                const fullTitle = titleElement.getAttribute('data-full-title');
                const episodeNumber = extractEpisodeNumber(fullTitle);
                const episodeNumberSpan = titleElement.querySelector('.episode-number');
                if (episodeNumberSpan) {
                    episodeNumberSpan.textContent = episodeNumber;
                }
            });

            // Trigger Scrape Button
            const triggerScrapeButton = document.getElementById('triggerScrapeButton');

            triggerScrapeButton.addEventListener('click', function() {
                // Disable the button and show loading state
                triggerScrapeButton.disabled = true;
                triggerScrapeButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Scraping...';

                // Make the API call
                fetch('/trigger-scrape', {
                    method: 'POST',
                })
                .then(response => response.json())
                .then(data => {
                    console.log('Success:', data);
                    // Re-enable the button after 5 seconds
                    // WebSocket will handle the page updates automatically
                    setTimeout(function() {
                        triggerScrapeButton.disabled = false;
                        triggerScrapeButton.innerHTML = '🔄 Get Updates';
                    }, 5000);
                })
                .catch((error) => {
                    console.error('Error:', error);
                    triggerScrapeButton.disabled = false;
                    triggerScrapeButton.innerHTML = '❌ Error - Try Again';
                });
            });

            // Preview Episode Check Functionality
            const previewCheckLinks = document.querySelectorAll('.preview-check-link');

            previewCheckLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    const episodeId = this.getAttribute('data-episode-id');
                    const episodeTitle = this.getAttribute('data-episode-title');
                    const showId = this.getAttribute('data-show-id');
                    const episodeCard = this.closest('.episode-card');
                    const previewIndicator = this.querySelector('.preview-indicator');

                    // Add shimmer animation class
                    episodeCard.classList.add('checking-preview');

                    // Update the preview indicator text
                    previewIndicator.innerHTML = '<i class="fas fa-sync fa-spin"></i> Preview (checking...)';

                    // Make the API call to check the episode
                    console.log(`Checking preview for episode ID: ${episodeId}`);
                    fetch(`/api/check-preview/${episodeId}`, {
                        method: 'GET',
                    })
                    .then(response => {
                        console.log('Response status:', response.status);
                        return response.json();
                    })
                    .then(data => {
                        console.log('Preview check result:', data);

                        // Remove the shimmer animation
                        episodeCard.classList.remove('checking-preview');

                        if (data.status === 'updated') {
                            // Episode was updated - no longer a preview
                            episodeCard.classList.remove('preview-episode');

                            // Remove the preview indicator
                            previewIndicator.remove();

                            // Create a completely new link element
                            const newLink = document.createElement('a');
                            newLink.href = `/show/${showId}`;
                            newLink.className = 'episode-link';

                            // Copy the inner content from the old link (except the preview indicator which was removed)
                            const showTitle = this.querySelector('.episode-show-title').cloneNode(true);
                            const episodeTitle = this.querySelector('.episode-title').cloneNode(true);

                            // Make sure the episode number is still displayed correctly
                            const fullTitle = episodeTitle.getAttribute('data-full-title');
                            const episodeNumber = extractEpisodeNumber(fullTitle);
                            const episodeNumberSpan = episodeTitle.querySelector('.episode-number');
                            if (episodeNumberSpan) {
                                episodeNumberSpan.textContent = episodeNumber;
                            }

                            // Add the content to the new link
                            newLink.appendChild(showTitle);
                            newLink.appendChild(episodeTitle);

                            // Replace the old link with the new one
                            this.parentNode.replaceChild(newLink, this);

                            console.log('Link replaced with new link to show page');

                            // Add a success message that fades out
                            const successMsg = document.createElement('div');
                            successMsg.className = 'update-success-message';
                            successMsg.innerHTML = '<i class="fas fa-check-circle"></i> Updated! Full episode available';
                            episodeCard.appendChild(successMsg);

                            // Remove the success message after 5 seconds
                            setTimeout(() => {
                                successMsg.style.opacity = '0';
                                setTimeout(() => successMsg.remove(), 1000);
                            }, 4000);
                        } else {
                            // Still a preview
                            previewIndicator.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Preview (still unavailable)';

                            // After 3 seconds, change back to the original text
                            setTimeout(() => {
                                previewIndicator.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Preview (Click to re-check)';
                            }, 3000);
                        }
                    })
                    .catch((error) => {
                        console.error('Error checking preview:', error);
                        episodeCard.classList.remove('checking-preview');
                        previewIndicator.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Preview (error checking)';

                        // After 3 seconds, change back to the original text
                        setTimeout(() => {
                            previewIndicator.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Preview (Click to re-check)';
                        }, 3000);
                    });
                });
            });
        });
    </script>

    <style>
        /* Styling for watched episodes */
        .watched-episode {
            opacity: 0.8;
            background-color: rgba(40, 167, 69, 0.07); /* Light green background with reduced opacity */
        }

        .watched-indicator {
            font-size: 0.8rem;
            color: #28a745;
            margin-top: 5px;
            font-weight: bold;
        }

        /* Styling for preview episodes */
        .preview-episode {
            background-color: rgba(255, 193, 7, 0.15); /* Light yellow background with reduced opacity */
            border-left: 3px solid #ffc107 !important; /* Yellow border */
        }

        .preview-indicator {
            font-size: 0.8rem;
            color: #ffc107;
            margin-top: 5px;
            font-weight: bold;
        }

        /* Shimmer animation for checking preview */
        @keyframes shimmer {
            0% {
                background-position: -200% 0;
            }
            100% {
                background-position: 200% 0;
            }
        }

        .checking-preview {
            position: relative;
            overflow: hidden;
        }

        .checking-preview::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                        rgba(255, 193, 7, 0) 0%,
                        rgba(255, 193, 7, 0.2) 50%,
                        rgba(255, 193, 7, 0) 100%);
            background-size: 200% 100%;
            animation: shimmer 1.5s infinite;
            pointer-events: none;
            z-index: 1;
        }

        /* Success message styling */
        .update-success-message {
            font-size: 0.8rem;
            color: #28a745;
            margin-top: 5px;
            font-weight: bold;
            transition: opacity 1s ease;
        }

        /* Episode title styling */
        .episode-title {
            font-size: 0.9rem;
            font-weight: 500;
            margin-bottom: 5px;
        }

        .episode-show-title {
            font-weight: bold;
            margin-bottom: 3px;
        }
    </style>
</body>
</html>
