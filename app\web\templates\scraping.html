{% extends "base.html" %}

{% block title %}Re-checking Preview Episode{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4>Re-checking Preview Episode</h4>
                </div>
                <div class="card-body text-center">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <h5 class="card-title">{{ title }}</h5>
                    <p class="card-text">{{ message }}</p>
                    <p>This will only take a moment...</p>
                    <div class="progress mb-3">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                    </div>
                    <p class="text-muted small">You will be redirected back to the calendar when complete.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Animate the progress bar
    let progress = 0;
    const progressBar = document.querySelector('.progress-bar');
    const interval = setInterval(() => {
        progress += 5;
        progressBar.style.width = `${progress}%`;
        if (progress >= 100) {
            clearInterval(interval);
            // Redirect back to the calendar page
            window.location.href = "{{ redirect_url }}";
        }
    }, 200); // 20 seconds total (200ms * 100 steps)
</script>
{% endblock %}
