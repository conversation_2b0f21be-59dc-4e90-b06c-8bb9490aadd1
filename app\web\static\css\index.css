/* Base styles */
body {
    background-color: #1e1e2f;
    color: #e0e0e0;
    background: url('/static/images/background.webp') no-repeat center center fixed;
    background-size: cover;
    background-attachment: fixed;
}

.navbar {
    background-color: #2d2d3a;
    color: #e0e0e0;
}

.sidebar {
    background-color: rgba(45, 45, 58, 0.5); /* More transparent than main content */
    color: #ffffff;
    backdrop-filter: blur(8px);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    position: fixed; /* Fix the sidebar in place */
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100; /* Ensure it stays on top of other content */
    overflow-y: auto; /* Allow scrolling within the sidebar if needed */
    height: 100vh; /* Full viewport height */
    width: 250px; /* Fixed width for sidebar */
    padding: 20px; /* Add padding to the sidebar */
}

a, a.nav-link, a.nav-link.active {
    color: #9ecaff;
    text-shadow: 0 0 3px rgba(0, 0, 0, 0.5);
}

.btn-outline-dark {
    color: #e0e0e0;
    border-color: #e0e0e0;
}

/* Table styles */
.table {
    color: #e0e0e0;
    background-color: rgba(45, 45, 58, 0.5);
}

/* Table headers and column labels */
th, .table-header, .column-header, .views-header, .actions-header {
    color: #ffffff !important;
    font-weight: bold;
    text-shadow: 0 0 4px rgba(0, 0, 0, 0.6);
    background-color: rgba(30, 30, 45, 0.8) !important;
}

.table-striped > tbody > tr:nth-of-type(odd) {
    background-color: rgba(55, 55, 68, 0.5);
}

.table-hover > tbody > tr:hover {
    background-color: rgba(65, 65, 78, 0.5);
}

/* Card and container styles */
.card-body {
    background-color: rgba(45, 45, 58, 0.7);
}

.btn-success {
    background-color: #198754;
    border-color: #198754;
}

.alert-info {
    background-color: #2d2d3a;
    color: #e0e0e0;
    border-color: #3a3a4a;
}

/* Heading styles */
h1, h2, h3, h4, h5, h6, .card-title {
    color: #ffffff;
    text-shadow: 0 0 5px rgba(0, 0, 0, 0.7); /* Add shadow for better visibility */
}

/* Improve visibility of page headers */
.page-header, .section-header {
    color: #ffffff;
    font-weight: bold;
    text-shadow: 0 0 5px rgba(0, 0, 0, 0.7);
    margin-bottom: 1rem;
}

/* Form elements */
.form-check-input {
    background-color: #3a3a4a;
    border-color: #4d4d5a;
}

.form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* Unchanged styles */
.center-message {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    height: 100vh;
}

.checkbox-title {
    display: flex;
    align-items: center;
}

.checkbox-title .form-check {
    margin-right: 10px;
}

.badge-preview {
    background-color: #ffc107;
    color: #000;
    margin-left: 10px;
}

/* Glassy, semi-opaque effect for containers */
.container-fluid, .card {
    background-color: rgba(45, 45, 58, 0.65); /* Semi-transparent dark grey (reduced opacity by 20%) */
    backdrop-filter: blur(10px); /* Glassy effect */
    border-radius: 10px; /* Rounded corners */
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.2); /* Subtle shadow for depth */
}

/* Parallax effect adjustment */
.container-fluid {
    position: relative;
    z-index: 1; /* Ensure it's above the background */
    padding: 20px;
}

/* Content wrapper styles */
.content-wrapper {
    margin-left: 250px; /* Match sidebar width */
    min-height: 100vh;
}

/* Main content styles */
.main-content {
    padding: 20px;
}

/* Adjust main content header for better alignment with sidebar */
.main-content .border-bottom {
    padding-top: 10px;
    padding-bottom: 10px;
}

/* Responsive adjustments */
@media (max-width: 767.98px) {
    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
    }
    .content-wrapper {
        margin-left: 0;
    }
}

/* Target only the button inside the sidebar */
.sidebar button.btn-success {
    margin-left: 0; /* Remove left margin */
    width: 100%; /* Full width */
    font-weight: 600;
}

/* Sidebar content styling */
.sidebar-content {
    padding: 20px 0 10px; /* Adjusted top padding for better alignment with main content */
}

/* Logo container styling */
.sphere-logo-container {
    width: 100%;
    height: 200px; /* Fixed height */
    position: relative;
    margin: 0 auto 20px; /* Added bottom margin */
    overflow: hidden;
    border-radius: 50%; /* Makes the container circular */
    background-color: rgba(0, 0, 0, 0.25); /* Slightly darker background */
    box-shadow: 0 0 20px rgba(0, 170, 255, 0.3); /* Enhanced glow */
}

.sphere-logo-container canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

/* Improve sidebar buttons and navigation */
.sidebar .btn {
    font-weight: 600;
    text-shadow: 0 0 3px rgba(0, 0, 0, 0.5);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    margin: 5px 0;
}

.sidebar .nav-link {
    font-weight: 500;
    padding: 8px 15px;
    margin: 2px 0;
    border-radius: 5px;
    transition: background-color 0.2s;
}

.sidebar .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Improve visibility of Views and Actions headings */
.sidebar h6 {
    color: #ffffff;
    font-weight: bold;
    font-size: 1.1rem;
    margin-top: 0.5rem; /* Reduced top margin for better alignment */
    margin-bottom: 0.5rem;
    text-shadow: 0 0 4px rgba(0, 0, 0, 0.7);
}

/* Specific styling for the Views and Actions headings */
.nav-section h6 {
    letter-spacing: 0.5px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding-bottom: 10px; /* Increased padding to align with main content border */
    padding-top: 5px; /* Added top padding for better alignment */
}

/* Additional styles for better visibility */
.text-light {
    color: #ffffff !important;
    text-shadow: 0 0 4px rgba(0, 0, 0, 0.6);
}

/* Improve visibility of navigation and header text */
.navbar-brand, .nav-item, .nav-link, .header-text {
    color: #ffffff !important;
    text-shadow: 0 0 4px rgba(0, 0, 0, 0.7);
    font-weight: 500;
}

/* Logo Container - Additional Styling */
.sphere-logo-container {
    display: flex;
    justify-content: center;
    align-items: center;
    transition: box-shadow 0.3s ease;
}

.sphere-logo-container:hover {
    box-shadow: 0 0 30px rgba(0, 170, 255, 0.5); /* Enhanced glow on hover */
}

#sphere-container canvas {
    width: 100% !important;
    height: 100% !important;
    display: block;
}

/* Improve visibility of the sidebar title */
.sidebar h4 {
    color: #ffffff;
    font-weight: bold;
    text-shadow: 0 0 5px rgba(0, 0, 0, 0.7);
    margin-bottom: 1rem;
    letter-spacing: 0.5px;
}

.bg-dark {
    background-color: #1a1a1a !important;
}

.border-bottom {
    border-color: #444 !important;
}

/* Ensure table text is visible */
.table {
    --bs-table-color: #e0e0e0;
}

/* Episode count badge styling */
.badge.bg-info {
    font-size: 1rem;
    padding: 0.5rem 0.8rem;
    background-color: #0d6efd !important;
    color: white;
}

/* Show card styling */
.show-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    overflow: hidden;
    background-color: rgba(45, 55, 72, 0.8);
    border-color: rgba(74, 85, 104, 0.5);
}

.show-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.show-thumbnail {
    height: 280px; /* Increased height from 200px to 280px */
    object-fit: cover;
    object-position: center 25%; /* Position to show more of the image (slightly above center) */
    width: 100%;
    transition: transform 0.3s ease; /* Smooth transition for hover effect */
}

/* Add a subtle zoom effect on hover */
.show-card:hover .show-thumbnail {
    transform: scale(1.05);
}

.placeholder-thumbnail {
    height: 200px;
    background-color: #343a40;
    color: #adb5bd;
    font-weight: bold;
}

.show-card .card-title {
    font-size: 1rem;
    margin-bottom: 0.5rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.show-card .card-body {
    color: #e2e8f0;
    background-color: rgba(45, 55, 72, 0.7);
}

.show-card .card-footer {
    background-color: rgba(26, 32, 44, 0.8);
    border-color: rgba(74, 85, 104, 0.5);
}