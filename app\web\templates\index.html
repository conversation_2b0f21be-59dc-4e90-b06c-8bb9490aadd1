<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DonghuaStream Manager</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', path='images/favicon.ico') }}">

    <!-- Bootstrap 5 CDN -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet" crossorigin="anonymous">

    <!-- Link to external CSS -->
    <link href="/static/css/index.css" rel="stylesheet">
    <link href="/static/css/realtime.css" rel="stylesheet">

    <!-- Custom DS Logo Script -->
    <script src="/static/js/ds-logo.js"></script>
</head>
<body>
    {% with active_page='home' %}
    {% include "sidebar.html" %}
    {% endwith %}

    <div class="content-wrapper">
        <div class="container-fluid">
            <!-- Main content -->
            <main class="main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h2 class="h2">🎬 Unwatched Shows</h2>
                </div>

                <div class="row">
                    {% if no_unwatched_episodes %}
                    <div class="center-message">
                        <!-- Bootstrap Spinner -->
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-3">Waiting for next updates from Scraper...</p>
                        <p class="text-muted">Real-time updates enabled - no refresh needed!</p>
                    </div>
                    {% else %}
                        <!-- Show Grid -->
                        {% for show_id, title, thumbnail_path, unwatched_count in shows %}
                        <div class="col-lg-2 col-md-3 col-sm-4 mb-4">
                            <div class="card h-100 show-card">
                                <a href="/show/{{ show_id }}" class="text-decoration-none">
                                    {% if thumbnail_path %}
                                    <img src="{{ thumbnail_path }}" class="card-img-top show-thumbnail" alt="{{ title }}">
                                    {% else %}
                                    <div class="placeholder-thumbnail d-flex align-items-center justify-content-center">
                                        <span>No Image</span>
                                    </div>
                                    {% endif %}
                                    <div class="card-body text-center">
                                        <h5 class="card-title">{{ title }}</h5>
                                        {% if unwatched_count > 0 %}
                                        <span class="badge bg-primary">{{ unwatched_count }} Unwatched</span>
                                        {% else %}
                                        <span class="badge bg-secondary">All Watched</span>
                                        {% endif %}
                                    </div>
                                </a>
                                <div class="card-footer text-center">
                                    <form method="POST" action="/mark-not-watching" class="d-inline">
                                        <input type="hidden" name="show_id" value="{{ show_id }}">
                                        <button type="submit" class="btn btn-sm btn-danger">Not Watching</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% endif %}
                </div>
            </main>
        </div>
    </div> <!-- End content-wrapper -->

    <!-- Bootstrap 5 JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js" crossorigin="anonymous"></script>

    <!-- Real-time WebSocket Client -->
    <script src="/static/js/realtime.js"></script>

    <!-- Custom JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {


            // Trigger Scrape Button
            const triggerScrapeButton = document.getElementById('triggerScrapeButton');

            triggerScrapeButton.addEventListener('click', function() {
                // Disable the button and show loading state
                triggerScrapeButton.disabled = true;
                triggerScrapeButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Scraping...';

                // Make the API call
                fetch('/trigger-scrape', {
                    method: 'POST',
                })
                .then(response => response.json())
                .then(data => {
                    console.log('Success:', data);
                    // Re-enable the button after 5 seconds
                    // WebSocket will handle the page updates automatically
                    setTimeout(function() {
                        triggerScrapeButton.disabled = false;
                        triggerScrapeButton.innerHTML = '🔄 Get Updates';
                    }, 5000);
                })
                .catch((error) => {
                    console.error('Error:', error);
                    triggerScrapeButton.disabled = false;
                    triggerScrapeButton.innerHTML = '❌ Error - Try Again';
                });
            });


        });
    </script>

</body>
</html>
