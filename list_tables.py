import sqlite3
import os

# Get the absolute path to the database
db_path = os.path.join(os.getcwd(), 'app', 'database', 'donghuastream.db')
print(f"Database path: {db_path}")

# Connect to the database
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

# Get all table names
cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
tables = cursor.fetchall()

print("\nTables in the database:")
for table in tables:
    print(f"- {table[0]}")

# Close the connection
conn.close()
