import sqlite3
import os

def setup_database():
    # Define the absolute path to the database
    DATABASE_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'donghuastream.db')
    db_path = DATABASE_PATH

    conn = sqlite3.connect(db_path)
    c = conn.cursor()

    # Enable foreign key constraints
    c.execute("PRAGMA foreign_keys = ON")

    # Create the shows table with title as UNIQUE
    c.execute('''CREATE TABLE IF NOT EXISTS shows
                 (id INTEGER PRIMARY KEY, title TEXT UNIQUE, url TEXT, watching BOOLEAN DEFAULT 1, thumbnail_path TEXT)''')

    # Create the episodes table with an additional 'is_preview' field and 'created_at' timestamp
    c.execute('''CREATE TABLE IF NOT EXISTS episodes
                 (id INTEGER PRIMARY KEY, title TEXT,
                  episode_page_url TEXT UNIQUE, iframe_src TEXT,
                  show_id INTEGER, watched BOOLEAN DEFAULT 0,
                  is_preview BOOLEAN DEFAULT 0,
                  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                  FOREIGN KEY (show_id) REFERENCES shows(id))''')

    conn.commit()
    return conn, c

setup_database()
