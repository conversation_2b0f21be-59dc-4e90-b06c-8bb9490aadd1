// ds-logo.js - Simple DS logo with circular glint animation

document.addEventListener('DOMContentLoaded', function() {
    console.log('DS logo script loaded');

    // Check if the container exists
    const container = document.getElementById('sphere-container');
    if (!container) {
        console.error('Logo container not found');
        return;
    }

    // Create a canvas element
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    // Set canvas size to match container
    const updateCanvasSize = () => {
        const containerRect = container.getBoundingClientRect();
        const size = Math.min(containerRect.width, containerRect.height);
        canvas.width = size;
        canvas.height = size;
        
        // Make sure the canvas fills the container
        canvas.style.width = '100%';
        canvas.style.height = '100%';
    };
    
    updateCanvasSize();
    container.appendChild(canvas);
    
    // Animation variables
    let animationFrame;
    let angle = 0;
    
    // Draw the logo
    function drawLogo() {
        const width = canvas.width;
        const height = canvas.height;
        const centerX = width / 2;
        const centerY = height / 2;
        const radius = Math.min(width, height) / 2 - 10;
        
        // Clear canvas
        ctx.clearRect(0, 0, width, height);
        
        // Draw outer circle (dark background)
        ctx.beginPath();
        ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
        ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
        ctx.fill();
        
        // Draw circle border
        ctx.beginPath();
        ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)';
        ctx.lineWidth = 2;
        ctx.stroke();
        
        // Draw the glint animation (arc that moves around the circle)
        const glintLength = Math.PI / 2; // Length of the glint arc
        const startAngle = angle;
        const endAngle = startAngle + glintLength;
        
        // Create gradient for the glint
        const glintGradient = ctx.createLinearGradient(
            centerX + Math.cos(startAngle) * radius,
            centerY + Math.sin(startAngle) * radius,
            centerX + Math.cos(endAngle) * radius,
            centerY + Math.sin(endAngle) * radius
        );
        
        glintGradient.addColorStop(0, 'rgba(0, 170, 255, 0)');
        glintGradient.addColorStop(0.5, 'rgba(0, 170, 255, 0.8)');
        glintGradient.addColorStop(1, 'rgba(0, 170, 255, 0)');
        
        ctx.beginPath();
        ctx.arc(centerX, centerY, radius, startAngle, endAngle);
        ctx.strokeStyle = glintGradient;
        ctx.lineWidth = 4;
        ctx.stroke();
        
        // Draw "DS" text
        ctx.font = 'bold ' + Math.floor(radius * 0.8) + 'px Arial, sans-serif';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        
        // Add text shadow/glow
        ctx.shadowColor = 'rgba(0, 170, 255, 0.8)';
        ctx.shadowBlur = 15;
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 0;
        
        // Draw text
        ctx.fillStyle = 'white';
        ctx.fillText('DS', centerX, centerY);
        
        // Reset shadow
        ctx.shadowBlur = 0;
        
        // Update angle for next frame
        angle += 0.02;
        if (angle > Math.PI * 2) {
            angle = 0;
        }
        
        // Request next frame
        animationFrame = requestAnimationFrame(drawLogo);
    }
    
    // Start animation
    drawLogo();
    
    // Handle window resize
    window.addEventListener('resize', function() {
        updateCanvasSize();
    });
    
    // Clean up on page unload
    window.addEventListener('beforeunload', function() {
        if (animationFrame) {
            cancelAnimationFrame(animationFrame);
        }
    });
});
