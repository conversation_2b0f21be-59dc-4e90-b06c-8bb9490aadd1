# decode.py

import base64

def decode_base64_to_iframe(base64_string):
    """
    Decode a Base64-encoded iframe string and return the <iframe> HTML string.
    """
    try:
        decoded_bytes = base64.b64decode(base64_string)
        decoded_string = decoded_bytes.decode("utf-8")
        return decoded_string
    except Exception as e:
        raise ValueError(f"Error decoding Base64 string: {e}")
