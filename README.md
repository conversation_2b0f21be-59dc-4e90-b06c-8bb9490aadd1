# DonghuaStream Manager

DonghuaStream Manager is a web application for managing and streaming donghua (Chinese animation). It provides a centralized platform for scheduling, scraping, and presenting donghua content.

## Features

- Database management for storing donghua information
- Scheduling system for managing content updates
- Web scraping functionality to gather donghua data
- Web interface for user interaction and content display

## Project Structure

```
donghuastream_manager/
│
├── app/
│   ├── database/
│   │   ├── __init__.py
│   │   ├── db_manager.py
│   │   └── models.py
│   ├── scheduler/
│   │   ├── __init__.py
│   │   └── scheduler.py
│   ├── scraping/
│   │   ├── __init__.py
│   │   └── scraper.py
│   └── web/
│       ├── static/
│       │   ├── css/
│       │   ├── images/
│       │   └── js/
│       ├── templates/
│       ├── __init__.py
│       └── routes.py
│
├── config.py
├── dbsetup.py
├── main.py
├── requirements.txt
└── setup.py
```

## Installation

1. Clone the repository:
   ```
   git clone https://github.com/lydonator/donghuastream_manager.git
   cd donghuastream_manager
   ```

2. Create a virtual environment and activate it:
   ```
   python -m venv venv
   source venv/bin/activate  # On Windows, use `venv\Scripts\activate`

   OR

   conda create -n <envname> python=3.12 -y && conda activate <envname>
   
   ```

3. Install the required dependencies:
   ```
   pip install -r requirements.txt
   ```

4. Set up the database:
   ```
   python dbsetup.py
   ```

## Usage

1. Start the application:
   ```
   uvicorn main:app --reload
   ```

2. Open a web browser and navigate to `http://localhost:8000` (or the appropriate address if configured differently).

3. Use the web interface to manage and stream donghua content.

## Dependencies

The main dependencies for this project are listed in the `requirements.txt` file. Some key dependencies include:

- FastAPI: Web framework
- SQLAlchemy: Database ORM
- APScheduler: Task scheduling
- Requests: HTTP library for web scraping
- Uvicorn: ASGI server for running the FastAPI application

For a complete list of dependencies, refer to the `requirements.txt` file.

## License

This project is licensed under the MIT License. See the [LICENSE](LICENSE) file for details.