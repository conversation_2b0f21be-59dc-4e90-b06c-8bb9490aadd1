#!/usr/bin/env python3
"""
Test script to examine the homepage structure and find episode links.
"""

import sys
import os
import logging
import time

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.scraping.scraper import DonghuaScraper
from selenium.webdriver.common.by import By
from config import Config

def test_homepage_structure():
    """Test the homepage structure to find episode links."""
    
    # Configure logging for testing
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('test_homepage.log', encoding='utf-8')
        ],
        force=True
    )
    
    print("Testing homepage structure...")
    logging.info("Testing homepage structure...")
    
    # Create scraper instance
    scraper = DonghuaScraper()
    
    try:
        # Navigate to homepage
        homepage_url = "https://donghuastream.org/"
        print(f"Navigating to: {homepage_url}")
        scraper._driver.get(homepage_url)
        
        # Wait for page to load
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        
        WebDriverWait(scraper._driver, 20).until(
            EC.presence_of_element_located((By.TAG_NAME, 'body'))
        )
        
        time.sleep(5)  # Wait for JS to load
        
        # Save page source for analysis
        page_source = scraper._driver.page_source
        with open('homepage_source_debug.html', 'w', encoding='utf-8') as f:
            f.write(page_source)
        print("Homepage source saved to 'homepage_source_debug.html'")
        
        # Test different selectors to find episode links
        selectors_to_test = [
            "article",
            "article a",
            ".post",
            ".post a",
            ".episode",
            ".episode a",
            "a[href*='episode']",
            "a[href*='multiple-subtitles']",
            "a[title*='Episode']",
            ".entry-title a",
            ".post-title a",
            "h2 a",
            "h3 a",
            ".item a",
            ".content a",
            "[class*='item'] a",
            "[class*='post'] a",
            "[class*='episode'] a"
        ]
        
        episode_links = []
        
        for selector in selectors_to_test:
            try:
                elements = scraper._driver.find_elements(By.CSS_SELECTOR, selector)
                print(f"\nSelector '{selector}': Found {len(elements)} elements")
                
                if elements:
                    for i, elem in enumerate(elements[:5]):  # Show first 5
                        try:
                            href = elem.get_attribute('href')
                            title = elem.get_attribute('title') or elem.text.strip()
                            
                            # Check if this looks like an episode link
                            if href and ('episode' in href.lower() or 'multiple-subtitles' in href.lower()):
                                episode_links.append((title[:50], href))
                                print(f"  {i+1}. Episode Link: '{title[:50]}' -> {href}")
                            elif href:
                                print(f"  {i+1}. Other Link: '{title[:30]}' -> {href[:50]}...")
                            else:
                                print(f"  {i+1}. No href: '{title[:30]}'")
                        except Exception as e:
                            print(f"  {i+1}. Error getting element details: {e}")
            except Exception as e:
                print(f"Error with selector '{selector}': {e}")
        
        # Remove duplicates
        unique_episodes = list(set(episode_links))
        
        print(f"\n=== SUMMARY ===")
        print(f"Found {len(unique_episodes)} unique episode links:")
        for i, (title, url) in enumerate(unique_episodes[:10]):  # Show first 10
            print(f"  {i+1}. {title} -> {url}")
        
        if len(unique_episodes) > 10:
            print(f"  ... and {len(unique_episodes) - 10} more")
        
        # Test the best selector
        if unique_episodes:
            print(f"\nTesting with the most promising selector...")
            # Find which selector gave us the most results
            best_selector = "a[href*='episode']"  # Default fallback
            
            # Test this selector in the scraper context
            print(f"Recommended selector for scraper: {best_selector}")
        else:
            print("\nNo episode links found! The homepage structure may have changed significantly.")
        
        return unique_episodes
        
    except Exception as e:
        print(f"Error during testing: {e}")
        logging.error(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        scraper.close()

if __name__ == "__main__":
    print("Testing homepage structure...")
    print("=" * 50)
    
    result = test_homepage_structure()
    
    if result:
        print(f"\nTest completed successfully! Found {len(result)} episode links.")
        print("Check the logs and saved HTML file for detailed information.")
    else:
        print("\nTest failed or no episodes found. Check the logs for error details.")
