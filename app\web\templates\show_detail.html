<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ show[1] }} - DonghuaStream Manager</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', path='images/favicon.ico') }}">

    <!-- Bootstrap 5 CDN -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet" crossorigin="anonymous">

    <!-- Link to external CSS -->
    <link href="/static/css/index.css" rel="stylesheet">
    <link href="/static/css/realtime.css" rel="stylesheet">

    <!-- Custom DS Logo Script -->
    <script src="/static/js/ds-logo.js"></script>
</head>
<body>
    {% with active_page='show_detail' %}
    {% include "sidebar.html" %}
    {% endwith %}

    <div class="content-wrapper">
        <div class="container-fluid">
            <!-- Main content -->
            <main class="main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <div>
                        <a href="/" class="btn btn-outline-primary me-2">← Back</a>
                        <h2 class="h2 d-inline-block">{{ show[1] }}</h2>
                    </div>
                    <div>
                        <form method="POST" action="/mark-not-watching" class="d-inline">
                            <input type="hidden" name="show_id" value="{{ show[0] }}">
                            <button type="submit" class="btn btn-danger">❌ Not Watching</button>
                        </form>
                    </div>
                </div>

                <div class="row justify-content-center">
                    {% if show_has_no_episodes %}
                    <div class="center-message">
                        <p class="mt-3">All episodes for this show have been watched.</p>
                    </div>
                    {% else %}
                        {% for episode_id, title, iframe_src, is_preview, created_at in episodes %}
                        <div class="col-lg-8 col-md-10 mb-4">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <h5 class="card-title">
                                        {{ title }}
                                        {% if is_preview %}
                                            <span class="badge badge-preview">Preview</span>
                                        {% endif %}
                                        <small class="episode-date-text d-block mt-1">Added: {{ created_at }}</small>
                                    </h5>
                                    <div class="embed-responsive embed-responsive-16by9">
                                        <iframe class="embed-responsive-item" src="{{ iframe_src }}" frameborder="0" allowfullscreen="true"
                                            webkitallowfullscreen="true" mozallowfullscreen="true" width="100%" height="450"></iframe>
                                    </div>
                                    <div class="mt-3">
                                        <form method="POST" action="/mark-watched" class="d-inline-block me-2">
                                            <input type="hidden" name="episode_id" value="{{ episode_id }}">
                                            <input type="hidden" name="show_id" value="{{ show[0] }}">
                                            <button type="submit" class="btn btn-success">✅ Mark as Watched</button>
                                        </form>

                                        {% if is_preview %}
                                        <form method="POST" action="/unmark-preview" class="d-inline-block">
                                            <input type="hidden" name="episode_id" value="{{ episode_id }}">
                                            <input type="hidden" name="show_id" value="{{ show[0] }}">
                                            <button type="submit" class="btn btn-primary">🎬 Mark as Full Episode</button>
                                        </form>
                                        {% else %}
                                        <form method="POST" action="/mark-preview" class="d-inline-block">
                                            <input type="hidden" name="episode_id" value="{{ episode_id }}">
                                            <input type="hidden" name="show_id" value="{{ show[0] }}">
                                            <button type="submit" class="btn btn-warning">🎬 Mark as Preview</button>
                                        </form>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% endif %}
                </div>
            </main>
        </div>
    </div> <!-- End content-wrapper -->

    <!-- Bootstrap 5 JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js" crossorigin="anonymous"></script>

    <!-- Real-time WebSocket Client -->
    <script src="/static/js/realtime.js"></script>

    <!-- Custom JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {


            // Trigger Scrape Button
            const triggerScrapeButton = document.getElementById('triggerScrapeButton');

            triggerScrapeButton.addEventListener('click', function() {
                // Disable the button and show loading state
                triggerScrapeButton.disabled = true;
                triggerScrapeButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Scraping...';

                // Make the API call
                fetch('/trigger-scrape', {
                    method: 'POST',
                })
                .then(response => response.json())
                .then(data => {
                    console.log('Success:', data);
                    // Re-enable the button after 5 seconds
                    // WebSocket will handle the page updates automatically
                    setTimeout(function() {
                        triggerScrapeButton.disabled = false;
                        triggerScrapeButton.innerHTML = '🔄 Get Updates';
                    }, 5000);
                })
                .catch((error) => {
                    console.error('Error:', error);
                    triggerScrapeButton.disabled = false;
                    triggerScrapeButton.innerHTML = '❌ Error - Try Again';
                });
            });


        });
    </script>

    <!-- Custom CSS for episode date visibility -->
    <style>
        .episode-date-text {
            color: rgba(255, 255, 255, 0.8) !important;
            font-size: 0.85rem;
            font-weight: 500;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        }

        .badge-preview {
            background-color: #ffc107;
            color: #000;
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
        }
    </style>
</body>
</html>
