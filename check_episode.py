import sqlite3
import os

# Get the absolute path to the database
db_path = os.path.join(os.getcwd(), 'app', 'database', 'donghuastream.db')
print(f"Database path: {db_path}")

# Connect to the database
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

# Get the table names first
cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
tables = cursor.fetchall()
print("Tables in the database:")
for table in tables:
    print(f"- {table[0]}")

# Try to get the schema of the episodes table if it exists
if ('episodes',) in tables:
    cursor.execute("PRAGMA table_info(episodes)")
    columns = cursor.fetchall()
    print("\nColumns in episodes table:")
    for col in columns:
        print(f"- {col[1]} ({col[2]})")

# Try with a different table name
cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%episode%';")
episode_tables = cursor.fetchall()
print("\nTables that might contain episodes:")
for table in episode_tables:
    print(f"- {table[0]}")

if episode:
    print(f"ID: {episode[0]}")
    print(f"Title: {episode[1]}")
    print(f"Episode Page URL: {episode[2]}")
    print(f"iFrame Src: {episode[3]}")
    print(f"Is Preview: {episode[4]}")
else:
    print("Episode not found")

# Close the connection
conn.close()
