# app/scraping/scraper.py

import time
import logging
import os
import requests
import shutil
import re
import unicodedata
from urllib.parse import urlparse
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.firefox.service import Service as FirefoxService
from selenium.webdriver.firefox.options import Options as FirefoxOptions
from selenium.common.exceptions import (
    NoSuchElementException,
    TimeoutException,
    WebDriverException,
    InvalidSelectorException,
    StaleElementReferenceException
)
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from config import Config
from app.database.db_manager import DatabaseManager
from app.database.models import Show, Episode
from app.exceptions import PageLoadException, ScraperException
from abc import ABC, abstractmethod

class BaseScraper(ABC):
    def __init__(self):
        self._driver = self._init_driver()
        self.db_manager = DatabaseManager()

    def _init_driver(self):
        firefox_options = FirefoxOptions()
        # For newer Firefox versions (after v115), we need to add the --headless argument
        if Config.FIREFOX_HEADLESS:
            firefox_options.add_argument("--headless")
        # Keep the legacy headless property for backward compatibility
        firefox_options.headless = Config.FIREFOX_HEADLESS
        firefox_options.set_preference("webdriver.accept.untrusted.certs", Config.FIREFOX_ACCEPT_UNTRUSTED_CERTS)
        firefox_options.set_preference("webdriver.assume_untrusted_issuer", Config.FIREFOX_ASSUME_UNTRUSTED_ISSUER)
        firefox_options.set_preference("dom.webnotifications.enabled", not Config.FIREFOX_DISABLE_NOTIFICATIONS)
        firefox_options.set_preference("dom.push.enabled", not Config.FIREFOX_DISABLE_PUSH)

        try:
            service = FirefoxService(executable_path=Config.GECKODRIVER_PATH)
            driver = webdriver.Firefox(service=service, options=firefox_options)
            logging.info("Firefox WebDriver initialized successfully.")
            return driver
        except Exception as e:
            logging.error(f"Failed to initialize Firefox WebDriver: {e}")
            raise

    @abstractmethod
    def scrape(self):
        pass

    def close(self):
        if self._driver:
            self._driver.quit()
            logging.info("WebDriver quit.")
        self.db_manager.close()

class DonghuaScraper(BaseScraper):
    def __init__(self):
        super().__init__()
        self.base_url = Config.BASE_URL

    def scrape(self):
        logging.info("Starting scraping process.")

        # Attempt to acquire the lock to prevent concurrent runs
        if not Config.scraper_lock.acquire(blocking=False):
            logging.info("Scraper is already running. New run request ignored.")
            return "Scraper is already running. New run request ignored."

        summary = ""
        try:
            episode_list = self._scrape_episodes()
            logging.info(f"Scraper found {len(episode_list)} episodes to process.")

            new_shows_count = len(episode_list) if episode_list else 0
            current_date = time.strftime('%Y-%m-%d')
            summary = f"Returned {new_shows_count} new shows on {current_date}"

            # Print and log the summary
            print(summary)
            logging.info(summary)

            if episode_list:
                self._insert_episodes(episode_list)
                logging.info("Episodes processing completed.")
        except ScraperException as e:
            logging.error(f"Scraper error: {e}")
            summary = f"Scraper error: {e}"
        except Exception as e:
            logging.exception("An unexpected error occurred during scraping.")
            summary = f"Unexpected error during scraping: {e}"
        finally:
            self.close()
            Config.scraper_lock.release()
            logging.info("Scraper lock released.")
            return summary


    def _normalize_url(self, url):
        """Normalize URL to use the base domain from config."""
        if not url:
            return url

        # Parse the URL
        parsed_url = urlparse(url)
        base_parsed = urlparse(self.base_url)

        # Always use the domain from BASE_URL in config.py
        if parsed_url.netloc != base_parsed.netloc:
            # Create a new URL with the base domain
            normalized_url = url.replace(parsed_url.netloc, base_parsed.netloc)
            logging.info(f"Normalized URL: {url} -> {normalized_url}")
            return normalized_url

        # Check if the URL uses http instead of https
        if parsed_url.scheme != base_parsed.scheme:
            normalized_url = url.replace(f"{parsed_url.scheme}://", f"{base_parsed.scheme}://")
            logging.info(f"Normalized URL scheme: {url} -> {normalized_url}")
            return normalized_url

        return url

    def _scrape_episodes(self):
        logging.info(f"Accessing {self.base_url}")
        try:
            self._driver.get(self.base_url)
        except WebDriverException as e:
            logging.error(f"{Config.ERROR_PAGE_LOAD}: {e}")
            self._capture_debug_info('failed_page_load')
            raise PageLoadException(Config.ERROR_PAGE_LOAD)

        try:
            WebDriverWait(self._driver, Config.ARTICLE_LOAD_TIMEOUT).until(
                EC.presence_of_element_located((By.TAG_NAME, 'article'))
            )
        except TimeoutException:
            logging.error("Timeout while waiting for articles to load.")
            self._capture_debug_info('timeout_waiting_articles')
            raise PageLoadException("Timeout while waiting for articles to load.")

        self._scroll_down()

        try:
            article_elements = self._driver.find_elements(By.TAG_NAME, 'article')
        except Exception as e:
            logging.error(f"Error finding article elements: {e}")
            return []

        episode_set = set()
        episode_list = []
        for article in article_elements:
            try:
                link_tag = article.find_element(By.TAG_NAME, 'a')
                episode_url = link_tag.get_attribute('href')
                # Normalize the URL to use the base domain
                episode_url = self._normalize_url(episode_url)
                title = link_tag.get_attribute('title')
                if episode_url and episode_url not in episode_set:
                    episode_set.add(episode_url)
                    logging.info(f"Found episode: {title} -> {episode_url}")
                    episode_list.append((title, episode_url))
                else:
                    logging.info(f"{Config.DUPLICATE_EPISODE_SKIPPED}: {title} -> {episode_url}")
            except NoSuchElementException:
                logging.warning("No <a> tag found within <article>; skipping this article.")
            except Exception as e:
                logging.error(f"Error processing article: {e}")

        logging.info(f"Total episodes found: {len(episode_list)}")
        return episode_list

    def _get_episode_details(self, url, retries=3):
        # Normalize the URL to use the base domain
        url = self._normalize_url(url)
        logging.info(f"Accessing episode page: {url}")
        for attempt in range(retries):
            try:
                self._driver.get(url)

                WebDriverWait(self._driver, Config.PAGE_LOAD_TIMEOUT).until(
                    EC.presence_of_element_located((By.TAG_NAME, 'body'))
                )

                # Wait a bit longer for the page to fully load and JavaScript to execute
                logging.info("Waiting for page to fully load...")
                time.sleep(5)

                # Try to wait for iframes to load
                try:
                    WebDriverWait(self._driver, 10).until(
                        EC.presence_of_element_located((By.TAG_NAME, 'iframe'))
                    )
                    logging.info("Found at least one iframe on the page")
                except TimeoutException:
                    logging.info("No iframes found within timeout period")

                # We no longer rely on automatic preview detection
                # Instead, we use manual marking of previews
                # This is just a placeholder to maintain compatibility
                is_preview = False
                logging.info(f"Preview detection disabled - using manual preview marking instead")

                # Extract release date if available
                release_date = None
                try:
                    # Look for the release date in the span with class="updated"
                    updated_span = self._driver.find_element(By.XPATH, "//span[@class='updated']")
                    if updated_span:
                        date_str = updated_span.text.strip()
                        logging.info(f"Found release date: {date_str}")

                        # Parse the date
                        from datetime import datetime
                        try:
                            # Try to parse with format "April 13, 2025"
                            release_date = datetime.strptime(date_str, '%B %d, %Y').strftime('%Y-%m-%d %H:%M:%S')
                        except ValueError:
                            # Try alternative format without comma "April 13 2025"
                            release_date = datetime.strptime(date_str, '%B %d %Y').strftime('%Y-%m-%d %H:%M:%S')
                        logging.info(f"Extracted release date: {release_date}")
                except Exception as e:
                    logging.info(f"Could not extract release date from page {url}: {e}")
                    release_date = None

                try:
                    meta_tag = self._driver.find_element(By.XPATH, '//meta[@property="article:section"]')
                    show_title = meta_tag.get_attribute('content').strip()
                    logging.info(f"Found show: {show_title}")
                except Exception as e:
                    logging.info(f"Meta tag not found on page {url}. Assuming no episodes yet. Skipping.")
                    logging.debug(f"Exception details: {type(e).__name__}: {e}")
                    return 'no_iframe', None, None, False, None

                # Extract thumbnail URL
                thumbnail_url = None
                try:
                    # Look for the thumbnail image using various possible selectors
                    selectors = [
                        'img.wp-post-thumbnail',
                        'img.attachment-post-thumbnail',
                        'img[class*="post-thumbnail"]',
                        'img[data-src*="wp-content/uploads"]'
                    ]

                    for selector in selectors:
                        try:
                            thumbnail_img = self._driver.find_element(By.CSS_SELECTOR, selector)
                            if thumbnail_img:
                                # Try data-src first as it might contain the full-resolution image
                                thumbnail_url = thumbnail_img.get_attribute('data-src') or thumbnail_img.get_attribute('src')
                                if thumbnail_url:
                                    try:
                                        logging.info(f"Found thumbnail with selector '{selector}'")
                                        # Log the URL separately to avoid Unicode issues
                                        logging.info(f"Thumbnail URL: {thumbnail_url.encode('ascii', 'replace').decode('ascii')}")
                                    except Exception as e:
                                        logging.info(f"Error logging thumbnail URL: {str(e)}")
                                    break
                        except NoSuchElementException:
                            continue

                    if not thumbnail_url:
                        logging.info(f"No thumbnail found with standard selectors on page {url}")
                except Exception as e:
                    logging.info(f"Error finding thumbnail on page {url}: {e}")

                # Try multiple methods to find iframes
                src_list = []

                # Method 1: Direct iframe elements
                iframe_elements = self._driver.find_elements(By.TAG_NAME, 'iframe')
                for iframe in iframe_elements:
                    src = iframe.get_attribute('src')
                    if src and src != "about:blank":
                        src_list.append(src)
                        logging.info(f"Found iframe src via direct tag: {src}")

                # Method 2: Look for data-src attributes (sometimes iframes are lazy-loaded)
                if not src_list:
                    try:
                        iframe_elements = self._driver.find_elements(By.CSS_SELECTOR, 'iframe[data-src]')
                        for iframe in iframe_elements:
                            src = iframe.get_attribute('data-src')
                            if src and src != "about:blank":
                                src_list.append(src)
                                logging.info(f"Found iframe src via data-src: {src}")
                    except Exception as e:
                        logging.debug(f"Error finding iframes with data-src: {e}")

                # Method 3: Look for video elements
                if not src_list:
                    try:
                        video_elements = self._driver.find_elements(By.TAG_NAME, 'video')
                        for video in video_elements:
                            src = video.get_attribute('src')
                            if src:
                                src_list.append(src)
                                logging.info(f"Found video src: {src}")
                    except Exception as e:
                        logging.debug(f"Error finding video elements: {e}")

                # Method 4: Look for embed elements
                if not src_list:
                    try:
                        embed_elements = self._driver.find_elements(By.TAG_NAME, 'embed')
                        for embed in embed_elements:
                            src = embed.get_attribute('src')
                            if src:
                                src_list.append(src)
                                logging.info(f"Found embed src: {src}")
                    except Exception as e:
                        logging.debug(f"Error finding embed elements: {e}")

                # Method 5: Look for links to video sites
                if not src_list:
                    try:
                        video_site_patterns = ['dailymotion.com', 'youtube.com', 'youtu.be', 'vimeo.com']
                        link_elements = self._driver.find_elements(By.TAG_NAME, 'a')
                        for link in link_elements:
                            href = link.get_attribute('href')
                            if href and any(pattern in href for pattern in video_site_patterns):
                                src_list.append(href)
                                logging.info(f"Found video link: {href}")
                    except Exception as e:
                        logging.debug(f"Error finding video links: {e}")

                # Look specifically for the player-embed div which contains the main video
                try:
                    # First try to find by ID 'pembed'
                    player_embed = None
                    try:
                        player_embed = self._driver.find_element(By.ID, 'pembed')
                        if player_embed:
                            logging.info("Found player-embed div with ID 'pembed'")
                    except Exception as e:
                        logging.debug(f"Could not find element with ID 'pembed': {e}")

                    # If not found, try to find by class 'player-embed'
                    if not player_embed:
                        try:
                            player_embed = self._driver.find_element(By.CLASS_NAME, 'player-embed')
                            if player_embed:
                                logging.info("Found player-embed div with class 'player-embed'")
                        except Exception as e:
                            logging.debug(f"Could not find element with class 'player-embed': {e}")

                    # If we found a player embed div, look for the iframe inside it
                    if player_embed:
                        try:
                            iframe = player_embed.find_element(By.TAG_NAME, 'iframe')
                            if iframe:
                                src = iframe.get_attribute('src')
                                logging.info(f"Found iframe in player-embed div: {src}")
                                if src and src != "about:blank":
                                    # Clear any previous sources and use this one as it's the main player
                                    src_list = [src]
                                    logging.info(f"Found main player iframe src: {src}")
                                else:
                                    # Try to get data-src attribute
                                    data_src = iframe.get_attribute('data-src')
                                    if data_src and data_src != "about:blank":
                                        src_list = [data_src]
                                        logging.info(f"Found main player iframe data-src: {data_src}")
                        except Exception as e:
                            logging.debug(f"Error finding iframe in player embed: {e}")
                except Exception as e:
                    logging.debug(f"Error finding main player embed: {e}")

                # Save the page source for debugging
                try:
                    page_source = self._driver.page_source
                    logging.info(f"Page source length: {len(page_source)}")
                    # Log a snippet of the page source around any iframe tags
                    iframe_index = page_source.find('<iframe')
                    if iframe_index > 0:
                        start = max(0, iframe_index - 100)
                        end = min(len(page_source), iframe_index + 500)
                        iframe_snippet = page_source[start:end]
                        logging.info(f"Iframe snippet: {iframe_snippet}")
                except Exception as e:
                    logging.debug(f"Error analyzing page source: {e}")

                logging.info(f"Found {len(src_list)} iframe/video src(s) on the page.")

                if not src_list:
                    logging.info(f"{Config.NO_IFRAME_FOUND}: {url}")
                    return 'no_iframe', show_title, thumbnail_url, is_preview, release_date

                return src_list, show_title, thumbnail_url, is_preview, release_date
            except (TimeoutException, WebDriverException, InvalidSelectorException, StaleElementReferenceException) as e:
                logging.error(f"Attempt {attempt + 1} failed to load page {url}: {e}")
                self._capture_debug_info(f'retry_{attempt + 1}_page_load')
                time.sleep(5)
        logging.error(f"All {retries} attempts failed to load page {url}. Skipping.")
        raise PageLoadException(f"Failed to load page {url} after {retries} attempts.")

    def _insert_episodes(self, episode_list):
        for episode_title, episode_page_url in episode_list:
            try:
                existing_episode = Episode.get_by_episode_page_url(self.db_manager, episode_page_url)
                if existing_episode:
                    if existing_episode.is_preview == 0:
                        logging.info(f"Episode exists and is not a preview: {episode_title}")
                        continue
                    else:
                        iframe_srcs, show_title, thumbnail_url, is_preview, release_date = self._get_episode_details(episode_page_url)
                        if iframe_srcs == 'no_iframe':
                            logging.info(f"Skipped episode '{episode_title}' because no episodes are available.")
                            continue
                        if not iframe_srcs:
                            logging.warning(f"No iframe src found for episode: {episode_title}. Skipping update.")
                            continue

                        # Always update the iframe_src (even if it hasn't changed)
                        if existing_episode.iframe_src != iframe_srcs[0]:
                            logging.info(f"Updating iframe_src for episode '{episode_title}'")
                            logging.info(f"Old iframe_src: {existing_episode.iframe_src}")
                            logging.info(f"New iframe_src: {iframe_srcs[0]}")
                        else:
                            logging.info(f"No change in iframe_src for episode '{episode_title}'")

                        # Always update these fields for preview episodes
                        existing_episode.iframe_src = iframe_srcs[0]
                        existing_episode.watched = 0  # Set watched to 0 (unwatched)
                        existing_episode.is_preview = 0  # Always set to not a preview

                        logging.info(f"Setting is_preview=0 to make episode visible in the app")

                        # Logging before saving
                        logging.info(f"Updating episode '{existing_episode.title}' with watched={existing_episode.watched}, is_preview={existing_episode.is_preview}")

                        existing_episode.save(self.db_manager)

                        # Logging after saving
                        updated_episode = Episode.get_by_episode_page_url(self.db_manager, episode_page_url)
                        logging.info(f"After saving: watched={updated_episode.watched}, is_preview={updated_episode.is_preview}")

                        # Log the update status
                        logging.info(f"Updated episode and set is_preview=0: {episode_title}")
                else:
                    iframe_srcs, show_title, thumbnail_url, is_preview, release_date = self._get_episode_details(episode_page_url)
                    if iframe_srcs == 'no_iframe':
                        logging.info(f"Skipped episode '{episode_title}' because no episodes are available.")
                        continue
                    if not iframe_srcs:
                        logging.warning(f"No iframe src found for episode: {episode_title}. Skipping insertion.")
                        continue
                    show = Show.get_by_title(self.db_manager, show_title)
                    if not show:
                        # Create new show with thumbnail URL
                        show = Show(id=None, title=show_title, url=None)
                        show.save(self.db_manager)

                        # If we found a thumbnail, download and save it
                        if thumbnail_url:
                            self._download_and_save_thumbnail(thumbnail_url, show.id, show_title)
                    elif not show.thumbnail_path and thumbnail_url:
                        # If show exists but has no thumbnail, download and save it
                        self._download_and_save_thumbnail(thumbnail_url, show.id, show_title)

                    # Create the episode object
                    episode = Episode(
                        id=None,
                        title=episode_title,
                        episode_page_url=episode_page_url,
                        iframe_src=iframe_srcs[0],
                        show_id=show.id,
                        watched=0,  # Set watched to 0 (unwatched)
                        is_preview=1 if is_preview else 0,  # Set is_preview based on detection
                        created_at=release_date  # Use the extracted release date if available
                    )

                    if is_preview:
                        logging.info(f"Inserting new preview episode: {episode_title}")
                    episode.save(self.db_manager)
                    logging.info(f"Inserted new episode: {episode_title} -> {iframe_srcs[0]}")
                time.sleep(Config.SCREENSHOT_DELAY)
            except ScraperException as e:
                logging.error(f"Error processing episode '{episode_title}': {e}")
                continue
            except Exception as e:
                logging.error(f"Unexpected error processing episode '{episode_title}': {e}")
                continue

    def _scroll_down(self):
        try:
            # Wait for the body element to be present
            WebDriverWait(self._driver, Config.PAGE_LOAD_TIMEOUT).until(
                EC.presence_of_element_located((By.TAG_NAME, 'body'))
            )

            # Get initial height
            last_height = self._driver.execute_script("return document.body.scrollHeight")

            # Scroll down to load more content
            while True:
                self._driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                try:
                    WebDriverWait(self._driver, Config.SCROLL_WAIT_TIMEOUT).until(
                        lambda d: d.execute_script("return document.body.scrollHeight") > last_height
                    )
                    WebDriverWait(self._driver, Config.SCROLL_WAIT_TIMEOUT).until(
                        EC.presence_of_element_located((By.TAG_NAME, 'article'))
                    )
                except TimeoutException:
                    logging.info("No more content to scroll.")
                    break
                except Exception as e:
                    logging.error(f"Error while scrolling: {e}")
                    break

                new_height = self._driver.execute_script("return document.body.scrollHeight")
                if new_height == last_height:
                    break
                last_height = new_height
                time.sleep(Config.SCROLL_PAUSE_TIME)
        except Exception as e:
            logging.error(f"Error in _scroll_down: {e}")
            # Take a screenshot to help debug
            self._capture_debug_info('scroll_down_error')

    def _capture_debug_info(self, context):
        timestamp = time.strftime("%Y%m%d-%H%M%S")
        screenshot_path = os.path.join(Config.LOGS_DIR, f"{context}_screenshot_{timestamp}.png")
        page_source_path = os.path.join(Config.LOGS_DIR, f"{context}_page_source_{timestamp}.html")

        if not os.path.exists(Config.LOGS_DIR):
            os.makedirs(Config.LOGS_DIR)

        try:
            self._driver.save_screenshot(screenshot_path)
            logging.info(f"Captured screenshot: {screenshot_path}")
        except Exception as e:
            logging.error(f"Failed to capture screenshot: {e}")

        try:
            with open(page_source_path, 'w', encoding='utf-8') as f:
                f.write(self._driver.page_source)
            logging.info(f"Saved page source: {page_source_path}")
        except Exception as e:
            logging.error(f"Failed to save page source: {e}")

    def scrape_specific_episode(self, episode_page_url):
        """
        Scrape a specific episode by its URL.
        This is useful for re-checking preview episodes.

        Args:
            episode_page_url: The URL of the episode page to scrape
        """
        logging.info(f"Starting targeted scrape for episode: {episode_page_url}")

        # Attempt to acquire the lock to prevent concurrent runs
        if not Config.scraper_lock.acquire(blocking=False):
            logging.info("Scraper is already running. Targeted scrape request ignored.")
            return  # Exit if the scraper is already running

        try:
            # Check if the episode exists in the database
            existing_episode = Episode.get_by_episode_page_url(self.db_manager, episode_page_url)
            if not existing_episode:
                logging.error(f"Episode with URL {episode_page_url} not found in database.")
                return

            # Get the episode details
            iframe_srcs, show_title, thumbnail_url, is_preview, release_date = self._get_episode_details(episode_page_url)

            if iframe_srcs == 'no_iframe':
                logging.info(f"Skipped episode because no episodes are available.")
                return

            if not iframe_srcs:
                logging.warning(f"No iframe src found for episode. Skipping update.")
                return

            # Update the episode in the database
            # Always set is_preview to 0 to reset the preview status
            self.db_manager.cursor.execute(
                "UPDATE episodes SET iframe_src = ?, is_preview = 0 WHERE episode_page_url = ?",
                (iframe_srcs[0], episode_page_url)
            )
            self.db_manager.conn.commit()

            logging.info(f"Updated episode and reset preview status: {existing_episode.title}")

        except Exception as e:
            logging.error(f"Error in targeted scrape: {e}")
        finally:
            self.close()
            Config.scraper_lock.release()
            logging.info("Scraper lock released after targeted scrape.")

    def _download_and_save_thumbnail(self, thumbnail_url, show_id, show_title):
        """Download and save a thumbnail image from the episode page."""
        try:
            # Create thumbnails directory if it doesn't exist
            thumbnails_dir = os.path.join(Config.BASE_DIR, 'app', 'web', 'static', 'thumbnails')
            if not os.path.exists(thumbnails_dir):
                os.makedirs(thumbnails_dir)
                logging.info(f"Created thumbnails directory: {thumbnails_dir}")

            # Generate a filename based on the show title - remove non-ASCII characters
            # First, normalize the title to remove any special characters
            normalized_title = unicodedata.normalize('NFKD', show_title)
            # Then, remove any non-ASCII characters and replace spaces/special chars with underscores
            safe_title = re.sub(r'[^\x00-\x7F]', '', normalized_title)
            safe_title = re.sub(r'[^\w\-_]', '_', safe_title).strip('_')

            # If safe_title is empty after cleaning, use a generic name
            if not safe_title:
                safe_title = f"show_{show_id}"

            parsed_url = urlparse(thumbnail_url)
            original_filename = os.path.basename(parsed_url.path)
            extension = os.path.splitext(original_filename)[1] or '.jpg'
            filename = f"{safe_title}_{show_id}{extension}"
            filepath = os.path.join(thumbnails_dir, filename)

            # Download the image
            response = requests.get(thumbnail_url, stream=True)

            if response.status_code == 200:
                with open(filepath, 'wb') as f:
                    response.raw.decode_content = True
                    shutil.copyfileobj(response.raw, f)

                # Generate the relative path for the database
                relative_path = f"/static/thumbnails/{filename}"

                # Update the show record with the thumbnail path
                self.db_manager.update_show_thumbnail(show_id, relative_path)

                logging.info(f"Saved thumbnail for show {show_title}: {relative_path}")
                return relative_path
            else:
                logging.warning(f"Failed to download image from {thumbnail_url}: {response.status_code}")
                return None
        except Exception as e:
            logging.error(f"Error downloading thumbnail: {e}")
            return None
