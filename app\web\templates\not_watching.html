<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shows I'm Not Watching - DonghuaStream Manager</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', path='images/favicon.ico') }}">

    <!-- Bootstrap 5 CDN -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet" crossorigin="anonymous">

    <!-- Link to external CSS -->
    <link href="/static/css/index.css" rel="stylesheet">
    <link href="/static/css/realtime.css" rel="stylesheet">

    <!-- Custom DS Logo Script -->
    <script src="/static/js/ds-logo.js"></script>
</head>
<body>
    {% with active_page='not-watching' %}
    {% include "sidebar.html" %}
    {% endwith %}

    <div class="content-wrapper">
        <div class="container-fluid">
            <!-- Main content -->
            <main class="main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom bg-dark rounded p-2">
                    <h2 class="h2 text-light">🚫 Shows I'm Not Watching</h2>
                </div>

                <div class="row">
                    {% if not not_watching_shows %}
                    <div class="col-12 text-center">
                        <p class="text-light bg-dark p-3 rounded">You are not ignoring any shows. When you find shows you don't want to watch, they will appear here.</p>
                    </div>
                    {% else %}
                    <div class="col-12">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th class="text-light">Show Title</th>
                                        <th class="text-light">Unwatched Episodes</th>
                                        <th class="text-light">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for show_id, title, url, unwatched_count in not_watching_shows %}
                                    <tr>
                                        <td class="align-middle"><span class="text-light">{{ title }}</span></td>
                                        <td class="align-middle">
                                            <span class="text-light badge bg-info rounded-pill">{{ unwatched_count }}</span>
                                        </td>
                                        <td>
                                            <form method="POST" action="/mark-watching" class="d-inline">
                                                <input type="hidden" name="show_id" value="{{ show_id }}">
                                                <button type="submit" class="btn btn-success">✅ Now Watching</button>
                                            </form>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </main>
        </div>
    </div> <!-- End content-wrapper -->

    <!-- Bootstrap 5 JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js" crossorigin="anonymous"></script>

    <!-- Real-time WebSocket Client -->
    <script src="/static/js/realtime.js"></script>

    <!-- Custom JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // No functionality needed for this page
        });
    </script>
</body>
</html>
