# app/database/models.py

import logging

class Show:
    def __init__(self, id, title, url, watching=1, thumbnail_path=None):
        self.id = id
        self.title = title
        self.url = url
        self.watching = watching  # 1 for watching, 0 for not watching
        self.thumbnail_path = thumbnail_path  # Path to the thumbnail image

    @staticmethod
    def get_by_title(db_manager, title):
        db_manager.cursor.execute("SELECT id, title, url, watching, thumbnail_path FROM shows WHERE title = ?", (title,))
        result = db_manager.cursor.fetchone()
        if result:
            return Show(id=result[0], title=result[1], url=result[2], watching=result[3], thumbnail_path=result[4])
        else:
            return None

    def save(self, db_manager):
        if self.id:
            # Update existing record
            db_manager.cursor.execute("""
                UPDATE shows
                SET title = ?, url = ?, watching = ?, thumbnail_path = ?
                WHERE id = ?
            """, (self.title, self.url, self.watching, self.thumbnail_path, self.id))
        else:
            # Insert new record
            db_manager.cursor.execute("""
                INSERT INTO shows (title, url, watching, thumbnail_path)
                VALUES (?, ?, ?, ?)
            """, (self.title, self.url, self.watching, self.thumbnail_path))
            self.id = db_manager.cursor.lastrowid

        db_manager.conn.commit()  # Commit changes

class Episode:
    def __init__(self, id, title, episode_page_url, iframe_src, show_id, watched=0, is_preview=0, created_at=None):
        self.id = id
        self.title = title
        self.episode_page_url = episode_page_url
        self.iframe_src = iframe_src
        self.show_id = show_id
        self.watched = watched  # Use integers (0 or 1)
        self.is_preview = is_preview  # Use integers (0 or 1)
        self.created_at = created_at  # Timestamp when the episode was added

    @staticmethod
    def get_by_episode_page_url(db_manager, episode_page_url):
        db_manager.cursor.execute("""
            SELECT id, title, episode_page_url, iframe_src, show_id, watched, is_preview, created_at
            FROM episodes
            WHERE episode_page_url = ?
        """, (episode_page_url,))
        result = db_manager.cursor.fetchone()
        if result:
            return Episode(
                id=result[0],
                title=result[1],
                episode_page_url=result[2],
                iframe_src=result[3],
                show_id=result[4],
                watched=result[5],
                is_preview=result[6],
                created_at=result[7]
            )
        else:
            return None

    def save(self, db_manager):
        if self.id:
            # Update existing record
            logging.info(f"Updating episode ID {self.id} with is_preview={self.is_preview}")
            try:
                db_manager.cursor.execute("""
                    UPDATE episodes
                    SET title = ?, episode_page_url = ?, iframe_src = ?, show_id = ?, watched = ?, is_preview = ?
                    WHERE id = ?
                """, (self.title, self.episode_page_url, self.iframe_src, self.show_id, self.watched, self.is_preview, self.id))
                logging.info(f"Update SQL executed successfully for episode ID {self.id}")
            except Exception as e:
                logging.error(f"Error updating episode ID {self.id}: {e}")
        else:
            if self.created_at:
                # Insert new record with custom created_at date
                db_manager.cursor.execute("""
                    INSERT INTO episodes (title, episode_page_url, iframe_src, show_id, watched, is_preview, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (self.title, self.episode_page_url, self.iframe_src, self.show_id, self.watched, self.is_preview, self.created_at))
                logging.info(f"Inserted episode with custom date: {self.title} -> {self.created_at}")
            else:
                # Insert new record - created_at will be set automatically by DEFAULT CURRENT_TIMESTAMP
                db_manager.cursor.execute("""
                    INSERT INTO episodes (title, episode_page_url, iframe_src, show_id, watched, is_preview)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (self.title, self.episode_page_url, self.iframe_src, self.show_id, self.watched, self.is_preview))
            self.id = db_manager.cursor.lastrowid

        try:
            db_manager.conn.commit()  # Commit changes
            logging.info(f"Database commit successful for episode ID {self.id}")
        except Exception as e:
            logging.error(f"Error committing changes for episode ID {self.id}: {e}")

    # Add any other methods as needed
