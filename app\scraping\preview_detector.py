"""
Module to detect if an episode is a preview based on page content.
This is integrated with the DonghuaScraper class.
"""

import re
import logging

def is_preview_episode(driver):
    """
    Check if an episode is a preview by examining the page content.
    
    Args:
        driver: Selenium WebDriver instance with the episode page loaded
        
    Returns:
        bool: True if it's a preview, False otherwise
    """
    try:
        # Look for the episode title/number section in the sidebar
        # Based on the DOM structure you provided
        try:
            # First try to find the specific span element in the sidebar
            sidebar = driver.find_element("id", "sidebar")
            if sidebar:
                mainepisode = sidebar.find_element("id", "mainepisode")
                if mainepisode:
                    singlepisode = mainepisode.find_element("id", "singlepisode")
                    if singlepisode:
                        det_div = singlepisode.find_element("class name", "det")
                        if det_div:
                            span_element = det_div.find_element("tag name", "span")
                            if span_element and 'preview' in span_element.text.lower():
                                logging.info(f"Preview detected in span: {span_element.text}")
                                return True
        except Exception as e:
            logging.debug(f"Error finding preview in sidebar structure: {e}")
        
        # Fallback: Check for preview text in any span in the singlepisode section
        try:
            singlepisode = driver.find_element("id", "singlepisode")
            if singlepisode:
                spans = singlepisode.find_elements("tag name", "span")
                for span in spans:
                    if 'preview' in span.text.lower():
                        logging.info(f"Preview detected in span: {span.text}")
                        return True
        except Exception as e:
            logging.debug(f"Error finding preview in singlepisode spans: {e}")
        
        # Fallback: Look for preview text in the page title
        try:
            title_element = driver.find_element("tag name", "title")
            if title_element and 'preview' in title_element.text.lower():
                logging.info(f"Preview detected in page title: {title_element.text}")
                return True
        except Exception as e:
            logging.debug(f"Error finding preview in title: {e}")
        
        # Fallback: Look for preview text in the h1 element
        try:
            h1_elements = driver.find_elements("tag name", "h1")
            for h1 in h1_elements:
                if 'preview' in h1.text.lower():
                    logging.info(f"Preview detected in h1: {h1.text}")
                    return True
        except Exception as e:
            logging.debug(f"Error finding preview in h1: {e}")
        
        # Fallback: Check for preview in the URL
        current_url = driver.current_url
        if 'preview' in current_url.lower():
            logging.info(f"Preview detected in URL: {current_url}")
            return True
        
        # Final fallback: Check for preview pattern in the entire page source
        page_source = driver.page_source.lower()
        preview_patterns = [
            r'(\d+)\s+preview',
            r'episode\s+(\d+)\s+preview',
            r'preview\s+episode'
        ]
        
        for pattern in preview_patterns:
            if re.search(pattern, page_source, re.IGNORECASE):
                logging.info(f"Preview detected using pattern: {pattern}")
                return True
        
        return False
    except Exception as e:
        logging.error(f"Error checking if episode is preview: {e}")
        return False
