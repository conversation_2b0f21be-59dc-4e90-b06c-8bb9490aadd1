# setup.py
from setuptools import setup, find_packages

setup(
    name='donghuastream_manager',
    version='1.0.0',
    packages=find_packages(),
    install_requires=[
        'fastapi',
        'uvicorn',
        'selenium',
        'schedule',
        'jinja2',
        'sqlalchemy',
        # Add other dependencies as needed
    ],
    entry_points={
        'console_scripts': [
            'donghuastream_manager=app.main:app',
        ],
    },
)
 