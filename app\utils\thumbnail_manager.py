# app/utils/thumbnail_manager.py
import os
import requests
import logging
import urllib.parse
import re
from config import Config
import time
import random
import shutil

class ThumbnailManager:
    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.thumbnails_dir = os.path.join(Config.BASE_DIR, 'app', 'web', 'static', 'thumbnails')
        self._ensure_thumbnail_dir_exists()
        
    def _ensure_thumbnail_dir_exists(self):
        """Ensure the thumbnails directory exists."""
        if not os.path.exists(self.thumbnails_dir):
            os.makedirs(self.thumbnails_dir)
            logging.info(f"Created thumbnails directory: {self.thumbnails_dir}")
    
    def get_thumbnail_for_show(self, show_id, show_title):
        """
        Get a thumbnail for a show. If the show already has a thumbnail, return its path.
        Otherwise, search for a thumbnail and save it.
        """
        # Check if show already has a thumbnail
        self.db_manager.cursor.execute("SELECT thumbnail_path FROM shows WHERE id = ?", (show_id,))
        result = self.db_manager.cursor.fetchone()
        
        if result and result[0]:
            thumbnail_path = result[0]
            # Check if the file exists
            full_path = os.path.join(Config.BASE_DIR, thumbnail_path.lstrip('/'))
            if os.path.exists(full_path):
                logging.info(f"Using existing thumbnail for show {show_title}: {thumbnail_path}")
                return thumbnail_path
        
        # Search for a thumbnail
        try:
            # Construct search query
            search_query = f"{show_title} donghua anime cover art"
            
            # Use a simple approach to get an image URL
            image_url = self._search_image(search_query)
            
            if image_url:
                # Download and save the image
                return self._download_and_save_thumbnail(image_url, show_id, show_title)
            else:
                logging.warning(f"No thumbnail found for show: {show_title}")
                return None
        except Exception as e:
            logging.error(f"Error getting thumbnail for show {show_title}: {e}")
            return None
    
    def _search_image(self, query):
        """
        Simple method to search for an image. In a real application, you would use a proper API.
        This is a placeholder that should be replaced with a real image search API.
        """
        try:
            # For demonstration purposes, we'll use a simple approach
            # In a real application, you would use Google Custom Search API, Bing Image Search API, etc.
            search_url = f"https://www.google.com/search?q={urllib.parse.quote(query)}&tbm=isch"
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            response = requests.get(search_url, headers=headers)
            
            if response.status_code == 200:
                # Extract image URLs using regex (very basic approach)
                image_urls = re.findall(r'https://[^"\']*\.(?:jpg|jpeg|png|gif)', response.text)
                
                if image_urls:
                    # Return the first image URL that seems appropriate
                    for url in image_urls:
                        if 'google' not in url and len(url) < 500:  # Avoid Google's own images and very long URLs
                            return url
            
            return None
        except Exception as e:
            logging.error(f"Error searching for image: {e}")
            return None
    
    def _download_and_save_thumbnail(self, image_url, show_id, show_title):
        """Download and save a thumbnail image."""
        try:
            # Generate a filename based on the show title
            safe_title = re.sub(r'[^\w\-_]', '_', show_title)
            filename = f"{safe_title}_{int(time.time())}_{random.randint(1000, 9999)}.jpg"
            filepath = os.path.join(self.thumbnails_dir, filename)
            
            # Download the image
            response = requests.get(image_url, stream=True)
            
            if response.status_code == 200:
                with open(filepath, 'wb') as f:
                    response.raw.decode_content = True
                    shutil.copyfileobj(response.raw, f)
                
                # Generate the relative path for the database
                relative_path = f"/static/thumbnails/{filename}"
                
                # Update the show record with the thumbnail path
                self.db_manager.update_show_thumbnail(show_id, relative_path)
                
                logging.info(f"Saved thumbnail for show {show_title}: {relative_path}")
                return relative_path
            else:
                logging.warning(f"Failed to download image from {image_url}: {response.status_code}")
                return None
        except Exception as e:
            logging.error(f"Error downloading thumbnail: {e}")
            return None
